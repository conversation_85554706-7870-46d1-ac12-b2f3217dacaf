<?php
/**
 * Projects Page - Project showcase for tuber92sv
 *
 * Security measures implemented:
 * - Comprehensive security headers (XSS, clickjacking, MIME sniffing protection)
 * - Content Security Policy (CSP) for script and style protection
 * - Input validation and output sanitization
 * - Secure error handling without information disclosure
 * - Cache control for sensitive data protection
 * - Server-side processing to prevent source code exposure
 */

// Start output buffering to prevent header issues
ob_start();

// Security headers to prevent various attacks
header('X-Content-Type-Options: nosniff');
header('X-Frame-Options: DENY');
header('X-XSS-Protection: 1; mode=block');
header('Referrer-Policy: strict-origin-when-cross-origin');
header('X-Permitted-Cross-Domain-Policies: none');

// Content Security Policy - Allow only specific sources
header("Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline' fonts.googleapis.com; font-src 'self' fonts.gstatic.com; img-src 'self' data:; connect-src 'self'; frame-ancestors 'none'; base-uri 'self'; form-action 'self';");

// Cache control headers
header('Cache-Control: no-cache, no-store, must-revalidate, max-age=0');
header('Pragma: no-cache');
header('Expires: Thu, 01 Jan 1970 00:00:00 GMT');

// Prevent information disclosure
ini_set('display_errors', 0);
ini_set('log_errors', 1);
error_reporting(0);

// Input validation function
function validateInput($input) {
    if (empty($input)) return '';
    $input = trim($input);
    $input = stripslashes($input);
    $input = htmlspecialchars($input, ENT_QUOTES, 'UTF-8');
    return $input;
}

// Sanitize any GET/POST parameters (defensive programming)
$_GET = array_map('validateInput', $_GET);
$_POST = array_map('validateInput', $_POST);

// Page-specific security: Ensure this is accessed as intended
$current_page = basename($_SERVER['PHP_SELF']);
if ($current_page !== 'projects.php') {
    header('Location: projects.php', true, 302);
    exit();
}

// Future-proofing: Prepare for dynamic project content with security
$projects_data = array(); // Placeholder for future dynamic content
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>tuber92sv - Projects</title>
    <link href="https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@300;400;500;600&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary: #4a9eff;
            --primary-glow: rgba(74, 158, 255, 0.3);
            --bg: #0a0a0a;
            --bg-secondary: #111111;
            --text: #ffffff;
            --text-muted: #888888;
            --border: #222222;
            --grid: rgba(255, 255, 255, 0.03);
        }

        body {
            font-family: 'JetBrains Mono', monospace;
            background-color: var(--bg);
            color: var(--text);
            line-height: 1.6;
            overflow-x: hidden;
            position: relative;
        }

        /* Grid background */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image:
                linear-gradient(var(--grid) 1px, transparent 1px),
                linear-gradient(90deg, var(--grid) 1px, transparent 1px);
            background-size: 50px 50px;
            z-index: -1;
            animation: gridMove 20s linear infinite;
        }

        @keyframes gridMove {
            0% { transform: translate(0, 0); }
            100% { transform: translate(50px, 50px); }
        }

        /* Navigation */
        nav {
            position: fixed;
            top: 0;
            width: 100%;
            padding: 2rem 0;
            background: rgba(10, 10, 10, 0.8);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid var(--border);
            z-index: 100;
        }

        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: center;
            gap: 3rem;
        }

        .nav-link {
            color: var(--text-muted);
            text-decoration: none;
            font-weight: 400;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 2px;
            padding: 0.5rem 0;
            position: relative;
            transition: all 0.3s ease;
        }

        .nav-link.active {
            color: var(--primary);
        }

        .nav-link::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 0;
            height: 2px;
            background: var(--primary);
            transition: width 0.3s ease;
        }

        .nav-link:hover::after,
        .nav-link.active::after {
            width: 100%;
        }

        .nav-link:hover {
            color: var(--primary);
        }

        /* Main content */
        main {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 6rem 2rem 4rem;
        }

        .projects-content {
            max-width: 1000px;
            width: 100%;
            text-align: center;
        }

        .section-title {
            font-size: clamp(2.5rem, 6vw, 4rem);
            font-weight: 600;
            color: var(--primary);
            margin-bottom: 3rem;
            text-align: center;
            letter-spacing: -1px;
            text-shadow: 0 0 30px var(--primary-glow);
            opacity: 0;
            animation: fadeInUp 1s ease-out 0.5s forwards;
        }

        .projects-empty {
            background: var(--bg-secondary);
            border: 2px dashed var(--border);
            border-radius: 12px;
            padding: 4rem 2rem;
            margin-top: 3rem;
            transition: all 0.3s ease;
            opacity: 0;
            animation: fadeInUp 1s ease-out 0.7s forwards;
        }

        .projects-empty:hover {
            border-color: rgba(74, 158, 255, 0.5);
            background: rgba(74, 158, 255, 0.02);
        }

        .projects-empty-icon {
            font-size: 3rem;
            color: var(--text-muted);
            margin-bottom: 1.5rem;
            opacity: 0.5;
        }

        .projects-empty-text {
            font-size: 1.1rem;
            color: var(--text-muted);
            margin-bottom: 0.5rem;
        }

        .projects-empty-subtext {
            font-size: 0.9rem;
            color: var(--text-muted);
            opacity: 0.7;
        }

        .projects-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-top: 3rem;
        }

        .project-card {
            background: var(--bg-secondary);
            border: 1px solid var(--border);
            border-radius: 12px;
            padding: 2.5rem;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            text-align: left;
        }

        .project-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, var(--primary), rgba(74, 158, 255, 0.5));
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .project-card:hover::before {
            transform: scaleX(1);
        }

        .project-card:hover {
            border-color: var(--primary);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.3);
            transform: translateY(-4px);
        }

        .project-title {
            color: var(--text);
            font-weight: 500;
            margin-bottom: 1rem;
            font-size: 1.3rem;
        }

        .project-description {
            color: var(--text-muted);
            font-size: 0.95rem;
            line-height: 1.7;
            margin-bottom: 1.5rem;
        }

        .project-tech {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
        }

        .tech-tag {
            background: rgba(74, 158, 255, 0.1);
            border: 1px solid rgba(74, 158, 255, 0.3);
            color: var(--primary);
            padding: 0.4rem 0.8rem;
            border-radius: 6px;
            font-size: 0.8rem;
            font-weight: 400;
            transition: all 0.3s ease;
        }

        .tech-tag:hover {
            background: rgba(74, 158, 255, 0.2);
            border-color: var(--primary);
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Responsive */
        @media (max-width: 768px) {
            .nav-container {
                gap: 2rem;
            }

            .nav-link {
                font-size: 0.8rem;
            }

            main {
                padding: 8rem 1rem 4rem;
            }

            .projects-grid {
                grid-template-columns: 1fr;
            }

            .projects-empty {
                padding: 3rem 1.5rem;
            }
        }
    </style>
</head>
<body>
    <nav>
        <div class="nav-container">
            <a href="bio.php" class="nav-link">BIO</a>
            <a href="contact.php" class="nav-link">CONTACT</a>
            <a href="projects.php" class="nav-link active">PROJECTS</a>
        </div>
    </nav>

    <main>
        <div class="projects-content">
            <h2 class="section-title">PROJECTS</h2>
            <div class="projects-empty">
                <div class="projects-empty-icon">⚡</div>
                <p class="projects-empty-text">No projects yet</p>
                <p class="projects-empty-subtext">This space is ready for future projects and creations</p>
            </div>
        </div>
    </main>
</body>
</html>
<?php
// End output buffering and flush content
ob_end_flush();

// Additional security: Clear any sensitive variables
unset($current_page, $projects_data);

// Log successful page load
error_log("Projects page accessed from IP: " . ($_SERVER['REMOTE_ADDR'] ?? 'unknown'));
?>
