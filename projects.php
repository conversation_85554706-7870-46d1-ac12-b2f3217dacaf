<?php
/**
 * Projects Page - Project showcase for tuber92sv
 *
 * Security measures implemented:
 * - Comprehensive security headers (XSS, clickjacking, MIME sniffing protection)
 * - Content Security Policy (CSP) for script and style protection
 * - Input validation and output sanitization
 * - Secure error handling without information disclosure
 * - Cache control for sensitive data protection
 * - Server-side processing to prevent source code exposure
 */

// Start output buffering to prevent header issues
ob_start();

// Security headers to prevent various attacks
header('X-Content-Type-Options: nosniff');
header('X-Frame-Options: DENY');
header('X-XSS-Protection: 1; mode=block');
header('Referrer-Policy: strict-origin-when-cross-origin');
header('X-Permitted-Cross-Domain-Policies: none');

// Content Security Policy - Allow only specific sources
header("Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline' fonts.googleapis.com; font-src 'self' fonts.gstatic.com; img-src 'self' data:; connect-src 'self'; frame-ancestors 'none'; base-uri 'self'; form-action 'self';");

// Cache control headers
header('Cache-Control: no-cache, no-store, must-revalidate, max-age=0');
header('Pragma: no-cache');
header('Expires: Thu, 01 Jan 1970 00:00:00 GMT');

// Prevent information disclosure
ini_set('display_errors', 0);
ini_set('log_errors', 1);
error_reporting(0);

// Input validation function
function validateInput($input) {
    if (empty($input)) return '';
    $input = trim($input);
    $input = stripslashes($input);
    $input = htmlspecialchars($input, ENT_QUOTES, 'UTF-8');
    return $input;
}

// Sanitize any GET/POST parameters (defensive programming)
$_GET = array_map('validateInput', $_GET);
$_POST = array_map('validateInput', $_POST);

// Page-specific security: Ensure this is accessed as intended
$current_page = basename($_SERVER['PHP_SELF']);
if ($current_page !== 'projects.php') {
    header('Location: projects.php', true, 302);
    exit();
}

// Dynamic project management system
$projects_data = array();
$projects_error = false;
$json_file = 'projects.json';

// Security: Validate JSON file path and prevent directory traversal
$json_file = basename($json_file);
if (!preg_match('/^[a-zA-Z0-9._-]+\.json$/', $json_file)) {
    $projects_error = true;
    error_log("Invalid JSON file name attempted: " . $json_file);
}

// Load and parse projects data with comprehensive error handling
if (!$projects_error && file_exists($json_file)) {
    try {
        $json_content = file_get_contents($json_file);
        if ($json_content === false) {
            throw new Exception("Failed to read JSON file");
        }

        $decoded_data = json_decode($json_content, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new Exception("JSON parsing error: " . json_last_error_msg());
        }

        // Validate JSON structure
        if (!isset($decoded_data['projects']) || !is_array($decoded_data['projects'])) {
            throw new Exception("Invalid JSON structure: missing projects array");
        }

        $projects_data = $decoded_data['projects'];

        // Sanitize and validate each project
        foreach ($projects_data as $index => $project) {
            $projects_data[$index] = sanitizeProject($project);
        }

    } catch (Exception $e) {
        $projects_error = true;
        error_log("Projects JSON error: " . $e->getMessage());
        $projects_data = array();
    }
} else if (!$projects_error) {
    error_log("Projects JSON file not found: " . $json_file);
}

// Project data sanitization function
function sanitizeProject($project) {
    $sanitized = array();

    // Required fields with validation
    $sanitized['id'] = isset($project['id']) ? (int)$project['id'] : 0;
    $sanitized['name'] = isset($project['name']) ? validateInput($project['name']) : 'Untitled Project';
    $sanitized['description'] = isset($project['description']) ? validateInput($project['description']) : '';
    $sanitized['status'] = isset($project['status']) ? validateStatus($project['status']) : 'unknown';
    $sanitized['date_created'] = isset($project['date_created']) ? validateDate($project['date_created']) : '';
    $sanitized['featured'] = isset($project['featured']) ? (bool)$project['featured'] : false;

    // Optional fields with validation
    $sanitized['github_url'] = isset($project['github_url']) ? validateUrl($project['github_url']) : '';
    $sanitized['demo_url'] = isset($project['demo_url']) ? validateUrl($project['demo_url']) : '';
    $sanitized['image'] = isset($project['image']) ? validateInput($project['image']) : '';

    // Technologies array with validation
    $sanitized['technologies'] = array();
    if (isset($project['technologies']) && is_array($project['technologies'])) {
        foreach ($project['technologies'] as $tech) {
            $clean_tech = validateInput($tech);
            if (!empty($clean_tech)) {
                $sanitized['technologies'][] = $clean_tech;
            }
        }
    }

    return $sanitized;
}

// Validation helper functions
function validateStatus($status) {
    $valid_statuses = array('completed', 'in-progress', 'planned', 'unknown');
    $status = strtolower(trim($status));
    return in_array($status, $valid_statuses) ? $status : 'unknown';
}

function validateUrl($url) {
    if (empty($url)) return '';
    $url = trim($url);
    return filter_var($url, FILTER_VALIDATE_URL) ? htmlspecialchars($url, ENT_QUOTES, 'UTF-8') : '';
}

function validateDate($date) {
    if (empty($date)) return '';
    $date = trim($date);
    return preg_match('/^\d{4}-\d{2}-\d{2}$/', $date) ? htmlspecialchars($date, ENT_QUOTES, 'UTF-8') : '';
}

// Sort projects: featured first, then by date
usort($projects_data, function($a, $b) {
    if ($a['featured'] && !$b['featured']) return -1;
    if (!$a['featured'] && $b['featured']) return 1;
    return strcmp($b['date_created'], $a['date_created']);
});
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>tuber92sv - Projects</title>
    <link href="https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@300;400;500;600&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary: #4a9eff;
            --primary-glow: rgba(74, 158, 255, 0.3);
            --bg: #0a0a0a;
            --bg-secondary: #111111;
            --text: #ffffff;
            --text-muted: #888888;
            --border: #222222;
            --grid: rgba(255, 255, 255, 0.03);
        }

        body {
            font-family: 'JetBrains Mono', monospace;
            background-color: var(--bg);
            color: var(--text);
            line-height: 1.6;
            overflow-x: hidden;
            position: relative;
        }

        /* Grid background */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image:
                linear-gradient(var(--grid) 1px, transparent 1px),
                linear-gradient(90deg, var(--grid) 1px, transparent 1px);
            background-size: 50px 50px;
            z-index: -1;
            animation: gridMove 20s linear infinite;
        }

        @keyframes gridMove {
            0% { transform: translate(0, 0); }
            100% { transform: translate(50px, 50px); }
        }

        /* Navigation */
        nav {
            position: fixed;
            top: 0;
            width: 100%;
            padding: 2rem 0;
            background: rgba(10, 10, 10, 0.8);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid var(--border);
            z-index: 100;
        }

        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: center;
            gap: 3rem;
        }

        .nav-link {
            color: var(--text-muted);
            text-decoration: none;
            font-weight: 400;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 2px;
            padding: 0.5rem 0;
            position: relative;
            transition: all 0.3s ease;
        }

        .nav-link.active {
            color: var(--primary);
        }

        .nav-link::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 0;
            height: 2px;
            background: var(--primary);
            transition: width 0.3s ease;
        }

        .nav-link:hover::after,
        .nav-link.active::after {
            width: 100%;
        }

        .nav-link:hover {
            color: var(--primary);
        }

        /* Main content */
        main {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 6rem 2rem 4rem;
        }

        .projects-content {
            max-width: 1000px;
            width: 100%;
            text-align: center;
        }

        .section-title {
            font-size: clamp(2.5rem, 6vw, 4rem);
            font-weight: 600;
            color: var(--primary);
            margin-bottom: 3rem;
            text-align: center;
            letter-spacing: -1px;
            text-shadow: 0 0 30px var(--primary-glow);
            opacity: 0;
            animation: fadeInUp 1s ease-out 0.5s forwards;
        }

        .projects-empty {
            background: var(--bg-secondary);
            border: 2px dashed var(--border);
            border-radius: 12px;
            padding: 4rem 2rem;
            margin-top: 3rem;
            transition: all 0.3s ease;
            opacity: 0;
            animation: fadeInUp 1s ease-out 0.7s forwards;
        }

        .projects-empty:hover {
            border-color: rgba(74, 158, 255, 0.5);
            background: rgba(74, 158, 255, 0.02);
        }

        .projects-empty-icon {
            font-size: 3rem;
            color: var(--text-muted);
            margin-bottom: 1.5rem;
            opacity: 0.5;
        }

        .projects-empty-text {
            font-size: 1.1rem;
            color: var(--text-muted);
            margin-bottom: 0.5rem;
        }

        .projects-empty-subtext {
            font-size: 0.9rem;
            color: var(--text-muted);
            opacity: 0.7;
        }

        .projects-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-top: 3rem;
        }

        .project-card {
            background: var(--bg-secondary);
            border: 1px solid var(--border);
            border-radius: 12px;
            padding: 2.5rem;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            text-align: left;
        }

        .project-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, var(--primary), rgba(74, 158, 255, 0.5));
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .project-card:hover::before {
            transform: scaleX(1);
        }

        .project-card:hover {
            border-color: var(--primary);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.3);
            transform: translateY(-4px);
        }

        .project-title {
            color: var(--text);
            font-weight: 500;
            margin-bottom: 1rem;
            font-size: 1.3rem;
        }

        .project-description {
            color: var(--text-muted);
            font-size: 0.95rem;
            line-height: 1.7;
            margin-bottom: 1.5rem;
        }

        .project-tech {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
        }

        .tech-tag {
            background: rgba(74, 158, 255, 0.1);
            border: 1px solid rgba(74, 158, 255, 0.3);
            color: var(--primary);
            padding: 0.4rem 0.8rem;
            border-radius: 6px;
            font-size: 0.8rem;
            font-weight: 400;
            transition: all 0.3s ease;
        }

        .tech-tag:hover {
            background: rgba(74, 158, 255, 0.2);
            border-color: var(--primary);
        }

        /* Dynamic project cards */
        .project-card {
            background: var(--bg-secondary);
            border: 1px solid var(--border);
            border-radius: 12px;
            padding: 2.5rem;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            text-align: left;
            opacity: 0;
            animation: fadeInUp 1s ease-out forwards;
        }

        .project-card:nth-child(1) { animation-delay: 0.1s; }
        .project-card:nth-child(2) { animation-delay: 0.2s; }
        .project-card:nth-child(3) { animation-delay: 0.3s; }
        .project-card:nth-child(4) { animation-delay: 0.4s; }
        .project-card:nth-child(5) { animation-delay: 0.5s; }
        .project-card:nth-child(6) { animation-delay: 0.6s; }

        .project-card.featured {
            border-color: rgba(74, 158, 255, 0.3);
            background: linear-gradient(135deg, var(--bg-secondary), rgba(74, 158, 255, 0.05));
        }

        .project-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, var(--primary), rgba(74, 158, 255, 0.5));
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .project-card:hover::before {
            transform: scaleX(1);
        }

        .project-card:hover {
            border-color: var(--primary);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.3);
            transform: translateY(-4px);
        }

        .project-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 1rem;
        }

        .project-title {
            color: var(--text);
            font-weight: 500;
            font-size: 1.3rem;
            margin: 0;
        }

        .project-status {
            padding: 0.3rem 0.8rem;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .status-completed {
            background: rgba(46, 204, 113, 0.2);
            color: #2ecc71;
            border: 1px solid rgba(46, 204, 113, 0.3);
        }

        .status-in-progress {
            background: rgba(241, 196, 15, 0.2);
            color: #f1c40f;
            border: 1px solid rgba(241, 196, 15, 0.3);
        }

        .status-planned {
            background: rgba(155, 89, 182, 0.2);
            color: #9b59b6;
            border: 1px solid rgba(155, 89, 182, 0.3);
        }

        .status-unknown {
            background: rgba(149, 165, 166, 0.2);
            color: #95a5a6;
            border: 1px solid rgba(149, 165, 166, 0.3);
        }

        .project-description {
            color: var(--text-muted);
            font-size: 0.95rem;
            line-height: 1.7;
            margin-bottom: 1.5rem;
        }

        .project-tech {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            margin-bottom: 1.5rem;
        }

        .project-links {
            display: flex;
            gap: 1rem;
            margin-top: 1rem;
        }

        .project-link {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            background: rgba(74, 158, 255, 0.1);
            border: 1px solid rgba(74, 158, 255, 0.3);
            border-radius: 6px;
            color: var(--primary);
            text-decoration: none;
            font-size: 0.85rem;
            font-weight: 400;
            transition: all 0.3s ease;
        }

        .project-link:hover {
            background: rgba(74, 158, 255, 0.2);
            border-color: var(--primary);
            transform: translateY(-1px);
        }

        .project-date {
            color: var(--text-muted);
            font-size: 0.8rem;
            margin-top: 1rem;
            opacity: 0.7;
        }

        .featured-badge {
            position: absolute;
            top: 1rem;
            right: 1rem;
            background: var(--primary);
            color: var(--bg);
            padding: 0.3rem 0.6rem;
            border-radius: 4px;
            font-size: 0.7rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        /* Featured projects section */
        .featured-section {
            margin-bottom: 4rem;
        }

        .section-subtitle {
            font-size: 1.5rem;
            color: var(--text);
            margin-bottom: 2rem;
            text-align: center;
            font-weight: 400;
            opacity: 0.8;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Responsive */
        @media (max-width: 768px) {
            .nav-container {
                gap: 2rem;
            }

            .nav-link {
                font-size: 0.8rem;
            }

            main {
                padding: 8rem 1rem 4rem;
            }

            .projects-grid {
                grid-template-columns: 1fr;
            }

            .projects-empty {
                padding: 3rem 1.5rem;
            }
        }
    </style>
</head>
<body>
    <nav>
        <div class="nav-container">
            <a href="bio.php" class="nav-link">BIO</a>
            <a href="contact.php" class="nav-link">CONTACT</a>
            <a href="projects.php" class="nav-link active">PROJECTS</a>
        </div>
    </nav>

    <main>
        <div class="projects-content">
            <h2 class="section-title">PROJECTS</h2>

            <?php if (!empty($projects_data)): ?>
                <?php
                // Separate featured and regular projects
                $featured_projects = array_filter($projects_data, function($project) {
                    return $project['featured'];
                });
                $regular_projects = array_filter($projects_data, function($project) {
                    return !$project['featured'];
                });
                ?>

                <?php if (!empty($featured_projects)): ?>
                <div class="featured-section">
                    <h3 class="section-subtitle">Featured Projects</h3>
                    <div class="projects-grid">
                        <?php foreach ($featured_projects as $project): ?>
                            <div class="project-card featured">
                                <?php if ($project['featured']): ?>
                                    <div class="featured-badge">Featured</div>
                                <?php endif; ?>

                                <div class="project-header">
                                    <h3 class="project-title"><?php echo $project['name']; ?></h3>
                                    <span class="project-status status-<?php echo $project['status']; ?>">
                                        <?php echo ucfirst(str_replace('-', ' ', $project['status'])); ?>
                                    </span>
                                </div>

                                <p class="project-description"><?php echo $project['description']; ?></p>

                                <?php if (!empty($project['technologies'])): ?>
                                <div class="project-tech">
                                    <?php foreach ($project['technologies'] as $tech): ?>
                                        <span class="tech-tag"><?php echo $tech; ?></span>
                                    <?php endforeach; ?>
                                </div>
                                <?php endif; ?>

                                <?php if (!empty($project['github_url']) || !empty($project['demo_url'])): ?>
                                <div class="project-links">
                                    <?php if (!empty($project['github_url'])): ?>
                                        <a href="<?php echo $project['github_url']; ?>" class="project-link" target="_blank" rel="noopener noreferrer">
                                            <span>📁</span> GitHub
                                        </a>
                                    <?php endif; ?>
                                    <?php if (!empty($project['demo_url'])): ?>
                                        <a href="<?php echo $project['demo_url']; ?>" class="project-link" target="_blank" rel="noopener noreferrer">
                                            <span>🚀</span> Live Demo
                                        </a>
                                    <?php endif; ?>
                                </div>
                                <?php endif; ?>

                                <?php if (!empty($project['date_created'])): ?>
                                <div class="project-date">Created: <?php echo date('F j, Y', strtotime($project['date_created'])); ?></div>
                                <?php endif; ?>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
                <?php endif; ?>

                <?php if (!empty($regular_projects)): ?>
                <div class="regular-section">
                    <?php if (!empty($featured_projects)): ?>
                        <h3 class="section-subtitle">Other Projects</h3>
                    <?php endif; ?>
                    <div class="projects-grid">
                        <?php foreach ($regular_projects as $project): ?>
                            <div class="project-card">
                                <div class="project-header">
                                    <h3 class="project-title"><?php echo $project['name']; ?></h3>
                                    <span class="project-status status-<?php echo $project['status']; ?>">
                                        <?php echo ucfirst(str_replace('-', ' ', $project['status'])); ?>
                                    </span>
                                </div>

                                <p class="project-description"><?php echo $project['description']; ?></p>

                                <?php if (!empty($project['technologies'])): ?>
                                <div class="project-tech">
                                    <?php foreach ($project['technologies'] as $tech): ?>
                                        <span class="tech-tag"><?php echo $tech; ?></span>
                                    <?php endforeach; ?>
                                </div>
                                <?php endif; ?>

                                <?php if (!empty($project['github_url']) || !empty($project['demo_url'])): ?>
                                <div class="project-links">
                                    <?php if (!empty($project['github_url'])): ?>
                                        <a href="<?php echo $project['github_url']; ?>" class="project-link" target="_blank" rel="noopener noreferrer">
                                            <span>📁</span> GitHub
                                        </a>
                                    <?php endif; ?>
                                    <?php if (!empty($project['demo_url'])): ?>
                                        <a href="<?php echo $project['demo_url']; ?>" class="project-link" target="_blank" rel="noopener noreferrer">
                                            <span>🚀</span> Live Demo
                                        </a>
                                    <?php endif; ?>
                                </div>
                                <?php endif; ?>

                                <?php if (!empty($project['date_created'])): ?>
                                <div class="project-date">Created: <?php echo date('F j, Y', strtotime($project['date_created'])); ?></div>
                                <?php endif; ?>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
                <?php endif; ?>

            <?php else: ?>
                <!-- Fallback to empty state if no projects or error -->
                <div class="projects-empty">
                    <div class="projects-empty-icon">⚡</div>
                    <p class="projects-empty-text">No projects yet</p>
                    <p class="projects-empty-subtext">This space is ready for future projects and creations</p>
                </div>
            <?php endif; ?>
        </div>
    </main>
</body>
</html>
<?php
// End output buffering and flush content
ob_end_flush();

// Additional security: Clear any sensitive variables
unset($current_page, $projects_data, $projects_error, $json_file, $json_content, $decoded_data, $featured_projects, $regular_projects);

// Log successful page load with project count
$project_count = is_array($projects_data) ? count($projects_data) : 0;
error_log("Projects page accessed from IP: " . ($_SERVER['REMOTE_ADDR'] ?? 'unknown') . " - Projects loaded: " . $project_count);
?>
