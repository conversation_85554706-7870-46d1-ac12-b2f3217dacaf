{"projects": [{"id": 1, "name": "AI Chat Bot", "description": "An intelligent chatbot built with machine learning capabilities for natural language processing and conversation. Features include context awareness, sentiment analysis, and multi-language support.", "technologies": ["Python", "TensorFlow", "Flask", "JavaScript"], "status": "completed", "github_url": "https://github.com/tuber92sv/ai-chatbot", "demo_url": "https://chatbot-demo.tuber92sv.com", "image": "ai-chatbot.jpg", "date_created": "2024-01-15", "featured": true, "buttons": [{"label": "View Code", "url": "https://github.com/tuber92sv/ai-chatbot", "type": "primary", "icon": "📁"}, {"label": "Live Demo", "url": "https://chatbot-demo.tuber92sv.com", "type": "secondary", "icon": "🚀"}, {"label": "Documentation", "url": "https://docs.tuber92sv.com/ai-chatbot", "type": "external", "icon": "📖"}, {"label": "Download", "url": "https://releases.tuber92sv.com/ai-chatbot/latest", "type": "external", "icon": "⬇️"}]}, {"id": 2, "name": "Dinosaur Database", "description": "A comprehensive database of dinosaur species with detailed information, fossil records, and interactive timeline. Perfect for paleontology enthusiasts and researchers.", "technologies": ["PHP", "MySQL", "JavaScript", "CSS"], "status": "in-progress", "github_url": "https://github.com/tuber92sv/dino-database", "demo_url": "", "image": "dino-database.jpg", "date_created": "2024-02-20", "featured": true, "buttons": [{"label": "Source Code", "url": "https://github.com/tuber92sv/dino-database", "type": "primary", "icon": "📁"}, {"label": "Preview", "url": "https://preview.tuber92sv.com/dino-database", "type": "secondary", "icon": "👀"}, {"label": "Progress", "url": "https://github.com/tuber92sv/dino-database/projects/1", "type": "external", "icon": "📊"}]}, {"id": 3, "name": "Gaming Stats Tracker", "description": "Track your gaming statistics across multiple platforms including Roblox and Minecraft. Features achievement tracking, progress visualization, and friend comparisons.", "technologies": ["React", "Node.js", "MongoDB", "Express"], "status": "in-progress", "github_url": "https://github.com/tuber92sv/gaming-tracker", "demo_url": "https://gaming-stats.tuber92sv.com", "image": "gaming-tracker.jpg", "date_created": "2024-03-10", "featured": false, "buttons": [{"label": "GitHub", "url": "https://github.com/tuber92sv/gaming-tracker", "type": "primary", "icon": "📁"}, {"label": "Live Demo", "url": "https://gaming-stats.tuber92sv.com", "type": "secondary", "icon": "🚀"}, {"label": "API Docs", "url": "https://api.tuber92sv.com/gaming-tracker/docs", "type": "external", "icon": "📋"}]}, {"id": 4, "name": "Personal Website", "description": "This very website you're viewing! Built with modern PHP, featuring responsive design, security best practices, and dynamic content management.", "technologies": ["PHP", "HTML", "CSS", "JavaScript"], "status": "completed", "github_url": "https://github.com/tuber92sv/personal-website", "demo_url": "", "image": "personal-website.jpg", "date_created": "2024-03-25", "featured": false}, {"id": 5, "name": "ML Research Tool", "description": "A research tool for analyzing machine learning models and datasets. Includes data visualization, model comparison, and automated reporting features.", "technologies": ["Python", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Scikit-learn"], "status": "planned", "github_url": "", "demo_url": "", "image": "ml-research.jpg", "date_created": "2024-04-01", "featured": false}, {"id": 6, "name": "Fossil Identification App", "description": "Mobile app for identifying fossils using computer vision and machine learning. Upload a photo and get detailed information about the fossil species.", "technologies": ["React Native", "TensorFlow", "Firebase", "Python"], "status": "planned", "github_url": "", "demo_url": "", "image": "fossil-app.jpg", "date_created": "2024-04-15", "featured": true}]}