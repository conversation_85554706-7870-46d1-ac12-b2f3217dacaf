#!/usr/bin/env python3
"""
Injection Testing Suite Runner
Executes all injection testing scripts
WARNING: For educational and authorized testing purposes only!
"""

import os
import sys
import subprocess
import time

def run_script(script_name, description):
    """Run a Python script and capture output"""
    print(f"\n{'='*60}")
    print(f"RUNNING: {description}")
    print(f"Script: {script_name}")
    print(f"{'='*60}")
    
    try:
        # Run the script
        result = subprocess.run([sys.executable, script_name], 
                              capture_output=True, text=True, timeout=30)
        
        print("STDOUT:")
        print(result.stdout)
        
        if result.stderr:
            print("\nSTDERR:")
            print(result.stderr)
        
        print(f"\nReturn code: {result.returncode}")
        
    except subprocess.TimeoutExpired:
        print("Script timed out after 30 seconds")
    except FileNotFoundError:
        print(f"Script not found: {script_name}")
    except Exception as e:
        print(f"Error running script: {e}")
    
    print(f"{'='*60}")

def main():
    """Main function to run all injection tests"""
    print("INJECTION TESTING SUITE")
    print("WARNING: For educational and authorized testing only!")
    print("This will run multiple injection testing scripts")
    print("=" * 60)
    
    # List of scripts to run
    test_scripts = [
        ("injection_tests.py", "General Injection Testing Suite"),
        ("projects_injection_test.py", "Projects.json Specific Injection Tests"),
        ("exploit_demo.py", "Exploitation Demonstration")
    ]
    
    # Check if scripts exist
    missing_scripts = []
    for script, _ in test_scripts:
        if not os.path.exists(script):
            missing_scripts.append(script)
    
    if missing_scripts:
        print("Missing scripts:")
        for script in missing_scripts:
            print(f"  - {script}")
        print("Please ensure all scripts are in the current directory")
        return
    
    # Ask for confirmation
    response = input("\nDo you want to proceed with injection testing? (y/N): ")
    if response.lower() != 'y':
        print("Testing cancelled")
        return
    
    # Run each script
    start_time = time.time()
    
    for script, description in test_scripts:
        run_script(script, description)
        time.sleep(2)  # Brief pause between scripts
    
    end_time = time.time()
    
    # Summary
    print(f"\n{'='*60}")
    print("TESTING COMPLETE")
    print(f"Total time: {end_time - start_time:.2f} seconds")
    print(f"Scripts executed: {len(test_scripts)}")
    print("=" * 60)
    
    print("\nIMPORTANT REMINDERS:")
    print("1. These tests are for educational purposes only")
    print("2. Only use on systems you own or have permission to test")
    print("3. Review all generated files and clean up as needed")
    print("4. Implement proper security measures in production")
    
    # List generated files
    generated_files = [
        "test_projects.json",
        "malicious_projects.json",
        "test_php_processing.php"
    ]
    
    existing_files = [f for f in generated_files if os.path.exists(f)]
    if existing_files:
        print(f"\nGenerated test files (clean up manually):")
        for f in existing_files:
            print(f"  - {f}")

if __name__ == "__main__":
    main()
