# Security configuration for tuber92sv website
# Prevent direct access to sensitive files

# Deny access to JSON data files
<Files "*.json">
    Order Allow,<PERSON>y
    <PERSON> from all
</Files>

# Deny access to configuration files
<Files ".htaccess">
    Order Allow,Deny
    Deny from all
</Files>

# Prevent access to backup files
<Files "*~">
    Order Allow,<PERSON>y
    <PERSON> from all
</Files>

<Files "*.bak">
    Order Allow,<PERSON>y
    <PERSON> from all
</Files>

# Security headers for all files
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    Header always set X-Permitted-Cross-Domain-Policies none
</IfModule>

# Prevent server information disclosure
ServerTokens Prod
ServerSignature Off

# Disable directory browsing
Options -Indexes

# Custom error pages (optional)
ErrorDocument 403 /bio.php
ErrorDocument 404 /bio.php
