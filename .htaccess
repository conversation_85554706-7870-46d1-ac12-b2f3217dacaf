# ULTRA-SECURITY CONFIGURATION FOR TUBER92SV WEBSITE
# Enterprise-level security protections

# ============================================================================
# COMPREHENSIVE FILE ACCESS PROTECTION
# ============================================================================

# Deny access to ALL sensitive files
<FilesMatch "\.(json|log|txt|bak|backup|old|orig|save|conf|config|ini|sql|db)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# Deny access to hidden files
<FilesMatch "^\.">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# Deny access to PHP configuration files
<Files "*.php~">
    Order Allow,Deny
    Deny from all
</Files>

# Deny access to version control files
<FilesMatch "\.(git|svn|hg|bzr)">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# Deny access to security files
<Files "security_config.php">
    Order Allow,Deny
    Deny from all
</Files>

<Files "rate_limits.json">
    Order Allow,Deny
    Deny from all
</Files>

<Files "ip_blacklist.json">
    Order Allow,Deny
    Deny from all
</Files>

<Files "security.log">
    Order Allow,Deny
    Deny from all
</Files>

<Files "security_errors.log">
    Order Allow,Deny
    Deny from all
</Files>

# ============================================================================
# MAXIMUM SECURITY HEADERS
# ============================================================================

<IfModule mod_headers.c>
    # XSS Protection
    Header always set X-XSS-Protection "1; mode=block"

    # MIME Type Sniffing Protection
    Header always set X-Content-Type-Options "nosniff"

    # Clickjacking Protection
    Header always set X-Frame-Options "DENY"

    # Referrer Policy
    Header always set Referrer-Policy "strict-origin-when-cross-origin"

    # Cross-Domain Policy
    Header always set X-Permitted-Cross-Domain-Policies "none"

    # HSTS (HTTP Strict Transport Security)
    Header always set Strict-Transport-Security "max-age=31536000; includeSubDomains; preload"

    # Permissions Policy (disable dangerous features)
    Header always set Permissions-Policy "geolocation=(), microphone=(), camera=(), payment=(), usb=(), magnetometer=(), gyroscope=(), speaker=(), vibrate=(), fullscreen=(), sync-xhr=()"

    # Cross-Origin Policies
    Header always set Cross-Origin-Embedder-Policy "require-corp"
    Header always set Cross-Origin-Opener-Policy "same-origin"
    Header always set Cross-Origin-Resource-Policy "same-origin"

    # Content Security Policy (Ultra-Strict)
    Header always set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' https://fonts.googleapis.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self'; frame-ancestors 'none'; base-uri 'self'; form-action 'self'; upgrade-insecure-requests; block-all-mixed-content"

    # Cache Control for Security
    Header always set Cache-Control "no-cache, no-store, must-revalidate, private, max-age=0"
    Header always set Pragma "no-cache"
    Header always set Expires "Thu, 01 Jan 1970 00:00:00 GMT"

    # Remove server information
    Header always unset Server
    Header always unset X-Powered-By
</IfModule>

# ============================================================================
# SERVER SECURITY HARDENING
# ============================================================================

# Hide server information
ServerTokens Prod
ServerSignature Off

# Disable server-side includes
Options -Includes -ExecCGI

# Disable directory browsing
Options -Indexes

# Disable symbolic links
Options -FollowSymLinks

# Disable multiviews
Options -MultiViews

# ============================================================================
# PHP SECURITY HARDENING
# ============================================================================

<IfModule mod_php.c>
    # Disable dangerous PHP functions
    php_admin_value disable_functions "exec,passthru,shell_exec,system,proc_open,popen,curl_exec,curl_multi_exec,parse_ini_file,show_source,file_get_contents,file_put_contents,fopen,fwrite,fputs,fgets,fgetcsv,fgetss,fread,readfile,highlight_file,virtual,mail,fsockopen,pfsockopen,stream_socket_client"

    # Hide PHP version
    php_flag expose_php Off

    # Disable file uploads
    php_flag file_uploads Off

    # Disable remote file access
    php_flag allow_url_fopen Off
    php_flag allow_url_include Off

    # Session security
    php_flag session.cookie_httponly On
    php_flag session.cookie_secure On
    php_value session.cookie_samesite Strict
    php_flag session.use_strict_mode On

    # Error handling
    php_flag display_errors Off
    php_flag display_startup_errors Off
    php_flag log_errors On

    # Memory and execution limits
    php_value memory_limit 128M
    php_value max_execution_time 30
    php_value max_input_time 30
    php_value post_max_size 2M
    php_value upload_max_filesize 1M
</IfModule>

# ============================================================================
# REQUEST FILTERING AND PROTECTION
# ============================================================================

<IfModule mod_rewrite.c>
    RewriteEngine On

    # Block bad bots and scanners
    RewriteCond %{HTTP_USER_AGENT} (nikto|sqlmap|fimap|nessus|openvas|nmap|masscan|zmap|acunetix|w3af|skipfish|burp) [NC]
    RewriteRule .* - [F,L]

    # Block SQL injection attempts
    RewriteCond %{QUERY_STRING} (union|select|insert|delete|drop|create|alter|exec|script) [NC]
    RewriteRule .* - [F,L]

    # Block XSS attempts
    RewriteCond %{QUERY_STRING} (<script|javascript:|vbscript:|onload|onerror|onclick) [NC]
    RewriteRule .* - [F,L]

    # Block file inclusion attempts
    RewriteCond %{QUERY_STRING} (\.\.\/|\.\.\\|\/etc\/|\/proc\/|\/sys\/|\/dev\/|\/var\/|\/tmp\/) [NC]
    RewriteRule .* - [F,L]

    # Block command injection attempts
    RewriteCond %{QUERY_STRING} (;|&&|\|\||`|<|>|\$\(|\${) [NC]
    RewriteRule .* - [F,L]

    # Block PHP code injection attempts
    RewriteCond %{QUERY_STRING} (php://|expect://|zip://|data://|glob://|phar://) [NC]
    RewriteRule .* - [F,L]

    # Block Python injection attempts
    RewriteCond %{QUERY_STRING} (__import__|import\s+os|subprocess|os\.system) [NC]
    RewriteRule .* - [F,L]
</IfModule>

# ============================================================================
# RATE LIMITING AND DDOS PROTECTION
# ============================================================================

<IfModule mod_evasive24.c>
    DOSHashTableSize    2048
    DOSPageCount        5
    DOSPageInterval     1
    DOSSiteCount        50
    DOSSiteInterval     1
    DOSBlockingPeriod   3600
</IfModule>

# ============================================================================
# CUSTOM ERROR PAGES
# ============================================================================

ErrorDocument 400 /bio.php
ErrorDocument 401 /bio.php
ErrorDocument 403 /bio.php
ErrorDocument 404 /bio.php
ErrorDocument 405 /bio.php
ErrorDocument 406 /bio.php
ErrorDocument 410 /bio.php
ErrorDocument 429 /bio.php
ErrorDocument 500 /bio.php
ErrorDocument 502 /bio.php
ErrorDocument 503 /bio.php

# ============================================================================
# ADDITIONAL PROTECTIONS
# ============================================================================

# Prevent access to WordPress/CMS files (if any exist)
<FilesMatch "(wp-config|wp-admin|wp-includes|xmlrpc)">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# Block access to common attack files
<FilesMatch "(shell|backdoor|hack|exploit|malware|virus)">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# Force HTTPS (uncomment if SSL is available)
# <IfModule mod_rewrite.c>
#     RewriteEngine On
#     RewriteCond %{HTTPS} off
#     RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
# </IfModule>
