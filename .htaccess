# SECURE CONFIGURATION FOR TUBER92SV WEBSITE
# Basic security protections

# ============================================================================
# FILE ACCESS PROTECTION
# ============================================================================

# Deny access to JSON data files
<Files "*.json">
    Order Allow,Deny
    Deny from all
</Files>

# Deny access to log files
<Files "*.log">
    Order Allow,Deny
    Deny from all
</Files>

# Deny access to configuration files
<Files ".htaccess">
    Order Allow,Deny
    Deny from all
</Files>

# Prevent access to backup files
<Files "*~">
    Order Allow,Deny
    Deny from all
</Files>

<Files "*.bak">
    Order Allow,Deny
    Deny from all
</Files>

# ============================================================================
# BASIC SECURITY HEADERS
# ============================================================================

<IfModule mod_headers.c>
    # Basic security headers
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    Header always set X-Permitted-Cross-Domain-Policies none
</IfModule>

# ============================================================================
# SERVER SECURITY
# ============================================================================

# Disable directory browsing
Options -Indexes

# ============================================================================
# CUSTOM ERROR PAGES
# ============================================================================

ErrorDocument 403 /bio.php
ErrorDocument 404 /bio.php
ErrorDocument 500 /bio.php
