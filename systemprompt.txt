# System Prompt: Creating projects.json for Dynamic Project Management System

## File Purpose & Context

The `projects.json` file is a structured data file used by a PHP-based dynamic project showcase system to display project information on a personal website. This file serves as the content management backend for a projects page that dynamically renders project cards with information, technology tags, status indicators, and custom action buttons. The PHP system reads this JSON file, validates and sanitizes all data for security, and generates responsive HTML project cards that match the website's dark theme with blue accents.

## Complete JSON Schema

### Root Structure
```json
{
  "projects": [
    // Array of project objects
  ]
}
```

### Project Object Schema
```json
{
  "id": 1,                           // Required: Unique integer identifier
  "name": "Project Name",            // Required: String, project title (max 100 chars recommended)
  "description": "Detailed description", // Required: String, project description (max 500 chars recommended)
  "technologies": ["Tech1", "Tech2"], // Required: Array of strings, technologies used
  "status": "completed",             // Required: String, must be "completed", "in-progress", or "planned"
  "github_url": "https://github.com/user/repo", // Optional: String, valid GitHub URL
  "demo_url": "https://example.com", // Optional: String, valid demo/live site URL
  "image": "project-image.jpg",      // Optional: String, image filename (not implemented in current system)
  "date_created": "2024-01-15",      // Required: String, ISO date format YYYY-MM-DD
  "featured": true,                  // Required: Boolean, whether to highlight in featured section
  "buttons": [                       // Optional: Array of custom action button objects
    {
      "label": "Button Text",        // Required: String, button display text (max 20 chars recommended)
      "url": "https://example.com",  // Required: String, valid URL
      "type": "primary",             // Required: String, must be "primary", "secondary", or "external"
      "icon": "🔗"                   // Optional: String, emoji or text icon (max 10 chars)
    }
  ]
}
```

## Security Requirements

### URL Validation
- All URLs (`github_url`, `demo_url`, button `url`) must be valid HTTP/HTTPS URLs
- URLs are validated using PHP's `filter_var()` with `FILTER_VALIDATE_URL`
- External links automatically receive `rel="noopener noreferrer"` attributes

### Text Field Security
- All text fields are sanitized using `htmlspecialchars()` with `ENT_QUOTES` and `UTF-8` encoding
- Input is trimmed and stripped of slashes to prevent injection attacks
- Description and name fields should avoid HTML tags as they will be escaped

### File Protection
- The `projects.json` file must be protected from direct browser access via `.htaccess` rules
- File should not contain sensitive information like API keys or internal URLs
- Backup copies should follow the same protection rules

## Validation Rules

### Field Constraints
- **id**: Must be unique positive integer, used for internal tracking
- **name**: Required string, 1-100 characters recommended for display
- **description**: Required string, 1-500 characters recommended for card layout
- **technologies**: Required array, each technology should be 1-20 characters
- **status**: Must be exactly "completed", "in-progress", or "planned" (case-sensitive)
- **date_created**: Must follow ISO 8601 format YYYY-MM-DD, validated with regex
- **featured**: Must be boolean true/false, determines placement in featured section
- **buttons.type**: Must be "primary", "secondary", or "external" (case-sensitive)
- **buttons.icon**: Limited to 10 characters, typically emoji or short text

### Data Integrity
- Project IDs should be sequential and unique
- Featured projects are displayed first, then sorted by date_created (newest first)
- Empty or invalid fields are filtered out during PHP processing
- Malformed JSON will cause graceful fallback to empty state

## Example Projects

### Example 1: Featured Project with Custom Buttons
```json
{
  "id": 1,
  "name": "AI Chat Bot",
  "description": "An intelligent chatbot built with machine learning capabilities for natural language processing and conversation. Features include context awareness, sentiment analysis, and multi-language support.",
  "technologies": ["Python", "TensorFlow", "Flask", "JavaScript"],
  "status": "completed",
  "github_url": "https://github.com/username/ai-chatbot",
  "demo_url": "https://chatbot-demo.example.com",
  "image": "ai-chatbot.jpg",
  "date_created": "2024-01-15",
  "featured": true,
  "buttons": [
    {
      "label": "View Code",
      "url": "https://github.com/username/ai-chatbot",
      "type": "primary",
      "icon": "📁"
    },
    {
      "label": "Live Demo",
      "url": "https://chatbot-demo.example.com",
      "type": "secondary",
      "icon": "🚀"
    },
    {
      "label": "Documentation",
      "url": "https://docs.example.com/ai-chatbot",
      "type": "external",
      "icon": "📖"
    }
  ]
}
```

### Example 2: In-Progress Project with Legacy URLs
```json
{
  "id": 2,
  "name": "Mobile Game Tracker",
  "description": "Track gaming statistics across multiple mobile platforms with achievement monitoring and progress visualization.",
  "technologies": ["React Native", "Node.js", "MongoDB"],
  "status": "in-progress",
  "github_url": "https://github.com/username/game-tracker",
  "demo_url": "",
  "image": "game-tracker.jpg",
  "date_created": "2024-02-20",
  "featured": false
}
```

### Example 3: Planned Project with Minimal Configuration
```json
{
  "id": 3,
  "name": "Research Tool",
  "description": "A comprehensive research and data analysis tool for academic projects.",
  "technologies": ["Python", "Pandas", "Matplotlib"],
  "status": "planned",
  "github_url": "",
  "demo_url": "",
  "image": "",
  "date_created": "2024-04-01",
  "featured": false
}
```

## Best Practices

### File Organization
- Keep projects array sorted by importance (featured first) or chronologically
- Use consistent indentation (2 or 4 spaces) for readability
- Validate JSON syntax before deployment using online validators or IDE tools
- Maintain reasonable file size (recommended max 50 projects for performance)

### Naming Conventions
- Use descriptive project names that fit well in card layouts
- Technology names should match common conventions (e.g., "JavaScript" not "js")
- Button labels should be concise and action-oriented ("View Code", "Live Demo")
- Image filenames should follow web-safe naming (lowercase, hyphens, no spaces)

### Backward Compatibility
- Always include `github_url` and `demo_url` fields even if using custom buttons
- New projects can use either legacy URL fields or custom buttons array
- When adding new fields, make them optional to avoid breaking existing projects
- Test changes with a subset of projects before updating the entire file

### Content Guidelines
- Write descriptions that are informative but concise for card display
- Include relevant technologies that showcase skills and project scope
- Use realistic URLs or leave fields empty rather than placeholder text
- Set appropriate featured status to highlight best work (recommend max 3-4 featured)

### Maintenance
- Regularly review and update project statuses as work progresses
- Remove or archive very old projects to keep showcase current
- Update demo URLs if hosting changes or projects are moved
- Backup the file before making significant changes

## Integration Notes

The PHP system automatically handles:
- JSON parsing with error handling and fallback to empty state
- Data sanitization and XSS prevention for all fields
- Responsive card layout with hover effects and animations
- Status-based color coding (green=completed, yellow=in-progress, purple=planned)
- Featured project highlighting and sorting
- Backward compatibility between custom buttons and legacy URL fields

When creating or updating projects.json, ensure the file is valid JSON and follows the security requirements. The system will gracefully handle missing optional fields and invalid data by filtering them out during processing.
