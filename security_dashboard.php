<?php
/**
 * Security Dashboard - Monitor and manage website security
 * ULTRA-SECURE ACCESS CONTROL
 * 
 * Features:
 * - Real-time security monitoring
 * - Rate limiting statistics
 * - IP blacklist management
 * - Security log analysis
 * - Threat detection alerts
 */

// Include ultra-security configuration
require_once 'security_config.php';

// Ultra-secure access control
session_start();

// Check if user is authorized (simple auth for demo)
$authorized = false;
if (isset($_POST['auth_key']) && $_POST['auth_key'] === 'ultra_secure_2024') {
    $_SESSION['security_authorized'] = true;
}

if (isset($_SESSION['security_authorized']) && $_SESSION['security_authorized'] === true) {
    $authorized = true;
}

// If not authorized, show login form
if (!$authorized) {
    ?>
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Security Dashboard - Access Control</title>
        <style>
            body { 
                font-family: 'JetBrains Mono', monospace; 
                background: #0a0a0a; 
                color: #fff; 
                display: flex; 
                justify-content: center; 
                align-items: center; 
                height: 100vh; 
                margin: 0; 
            }
            .login-form { 
                background: #111; 
                padding: 2rem; 
                border-radius: 8px; 
                border: 1px solid #333; 
                text-align: center; 
            }
            input { 
                background: #222; 
                border: 1px solid #444; 
                color: #fff; 
                padding: 0.5rem; 
                margin: 0.5rem; 
                border-radius: 4px; 
            }
            button { 
                background: #4a9eff; 
                border: none; 
                color: #fff; 
                padding: 0.5rem 1rem; 
                border-radius: 4px; 
                cursor: pointer; 
            }
        </style>
    </head>
    <body>
        <div class="login-form">
            <h2>🔒 Security Dashboard Access</h2>
            <form method="POST">
                <input type="password" name="auth_key" placeholder="Authorization Key" required>
                <br>
                <button type="submit">Access Dashboard</button>
            </form>
            <p style="font-size: 0.8rem; color: #666; margin-top: 1rem;">
                Hint: ultra_secure_2024
            </p>
        </div>
    </body>
    </html>
    <?php
    exit();
}

// Dashboard functionality
global $ultra_security;

// Load security data
function loadSecurityData() {
    $data = [
        'rate_limits' => [],
        'blacklist' => [],
        'security_log' => []
    ];
    
    // Load rate limits
    if (file_exists('rate_limits.json')) {
        $rate_data = json_decode(file_get_contents('rate_limits.json'), true);
        $data['rate_limits'] = $rate_data ?: [];
    }
    
    // Load blacklist
    if (file_exists('ip_blacklist.json')) {
        $blacklist_data = json_decode(file_get_contents('ip_blacklist.json'), true);
        $data['blacklist'] = $blacklist_data ?: [];
    }
    
    // Load security log (last 100 entries)
    if (file_exists('security.log')) {
        $log_lines = file('security.log', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
        $data['security_log'] = array_slice(array_reverse($log_lines), 0, 100);
    }
    
    return $data;
}

$security_data = loadSecurityData();

// Handle actions
if (isset($_POST['action'])) {
    switch ($_POST['action']) {
        case 'clear_blacklist':
            file_put_contents('ip_blacklist.json', json_encode([]));
            $ultra_security->logSecurityEvent("Blacklist cleared by admin", 'INFO');
            break;
            
        case 'clear_rate_limits':
            file_put_contents('rate_limits.json', json_encode([]));
            $ultra_security->logSecurityEvent("Rate limits cleared by admin", 'INFO');
            break;
            
        case 'add_to_blacklist':
            if (!empty($_POST['ip_address'])) {
                $ip = filter_var($_POST['ip_address'], FILTER_VALIDATE_IP);
                if ($ip) {
                    $blacklist = $security_data['blacklist'];
                    if (!in_array($ip, $blacklist)) {
                        $blacklist[] = $ip;
                        file_put_contents('ip_blacklist.json', json_encode($blacklist));
                        $ultra_security->logSecurityEvent("IP manually added to blacklist: $ip", 'INFO');
                    }
                }
            }
            break;
    }
    
    // Reload data after action
    $security_data = loadSecurityData();
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Security Dashboard - tuber92sv</title>
    <link href="https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@300;400;500;600&display=swap" rel="stylesheet">
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        
        body {
            font-family: 'JetBrains Mono', monospace;
            background: #0a0a0a;
            color: #fff;
            line-height: 1.6;
        }
        
        .dashboard {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        .header {
            text-align: center;
            margin-bottom: 3rem;
            padding: 2rem;
            background: #111;
            border-radius: 8px;
            border: 1px solid #333;
        }
        
        .header h1 {
            color: #4a9eff;
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 3rem;
        }
        
        .stat-card {
            background: #111;
            padding: 1.5rem;
            border-radius: 8px;
            border: 1px solid #333;
            text-align: center;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: 600;
            color: #4a9eff;
            margin-bottom: 0.5rem;
        }
        
        .stat-label {
            color: #888;
            font-size: 0.9rem;
        }
        
        .section {
            background: #111;
            padding: 2rem;
            border-radius: 8px;
            border: 1px solid #333;
            margin-bottom: 2rem;
        }
        
        .section h2 {
            color: #4a9eff;
            margin-bottom: 1rem;
            font-size: 1.3rem;
        }
        
        .log-entry {
            background: #222;
            padding: 0.8rem;
            margin-bottom: 0.5rem;
            border-radius: 4px;
            font-size: 0.85rem;
            border-left: 3px solid #4a9eff;
        }
        
        .log-entry.warning {
            border-left-color: #ff9500;
        }
        
        .log-entry.critical {
            border-left-color: #ff4757;
        }
        
        .ip-list {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
        }
        
        .ip-tag {
            background: #ff4757;
            color: #fff;
            padding: 0.3rem 0.8rem;
            border-radius: 4px;
            font-size: 0.8rem;
        }
        
        .controls {
            display: flex;
            gap: 1rem;
            margin-bottom: 1rem;
            flex-wrap: wrap;
        }
        
        .btn {
            background: #4a9eff;
            color: #fff;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            cursor: pointer;
            font-family: inherit;
            font-size: 0.9rem;
        }
        
        .btn.danger {
            background: #ff4757;
        }
        
        .btn:hover {
            opacity: 0.8;
        }
        
        input[type="text"] {
            background: #222;
            border: 1px solid #444;
            color: #fff;
            padding: 0.5rem;
            border-radius: 4px;
            font-family: inherit;
        }
        
        .refresh-info {
            text-align: center;
            color: #666;
            font-size: 0.8rem;
            margin-top: 2rem;
        }
    </style>
</head>
<body>
    <div class="dashboard">
        <div class="header">
            <h1>🛡️ Security Dashboard</h1>
            <p>Real-time security monitoring for tuber92sv</p>
            <p style="font-size: 0.8rem; color: #666; margin-top: 1rem;">
                Last updated: <?php echo date('Y-m-d H:i:s'); ?>
            </p>
        </div>
        
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number"><?php echo count($security_data['blacklist']); ?></div>
                <div class="stat-label">Blocked IPs</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo count($security_data['rate_limits']); ?></div>
                <div class="stat-label">Rate Limited IPs</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo count($security_data['security_log']); ?></div>
                <div class="stat-label">Recent Log Entries</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">ACTIVE</div>
                <div class="stat-label">Security Status</div>
            </div>
        </div>
        
        <div class="section">
            <h2>🚫 IP Blacklist Management</h2>
            <div class="controls">
                <form method="POST" style="display: inline;">
                    <input type="hidden" name="action" value="clear_blacklist">
                    <button type="submit" class="btn danger" onclick="return confirm('Clear all blacklisted IPs?')">Clear Blacklist</button>
                </form>
                <form method="POST" style="display: inline-flex; gap: 0.5rem;">
                    <input type="hidden" name="action" value="add_to_blacklist">
                    <input type="text" name="ip_address" placeholder="IP Address" required>
                    <button type="submit" class="btn">Add to Blacklist</button>
                </form>
            </div>
            <div class="ip-list">
                <?php if (empty($security_data['blacklist'])): ?>
                    <p style="color: #666;">No IPs currently blacklisted</p>
                <?php else: ?>
                    <?php foreach ($security_data['blacklist'] as $ip): ?>
                        <span class="ip-tag"><?php echo htmlspecialchars($ip); ?></span>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>
        
        <div class="section">
            <h2>⚡ Rate Limiting Status</h2>
            <div class="controls">
                <form method="POST" style="display: inline;">
                    <input type="hidden" name="action" value="clear_rate_limits">
                    <button type="submit" class="btn danger" onclick="return confirm('Clear all rate limit data?')">Clear Rate Limits</button>
                </form>
            </div>
            <?php if (empty($security_data['rate_limits'])): ?>
                <p style="color: #666;">No rate limiting data available</p>
            <?php else: ?>
                <div style="max-height: 300px; overflow-y: auto;">
                    <?php foreach ($security_data['rate_limits'] as $ip => $data): ?>
                        <div class="log-entry">
                            <strong><?php echo htmlspecialchars($ip); ?></strong> - 
                            Requests: <?php echo $data['total_requests'] ?? 0; ?> | 
                            Recent: <?php echo count($data['minute_requests'] ?? []); ?>/min
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>
        
        <div class="section">
            <h2>📋 Security Log (Recent 100 entries)</h2>
            <div style="max-height: 400px; overflow-y: auto;">
                <?php if (empty($security_data['security_log'])): ?>
                    <p style="color: #666;">No security log entries available</p>
                <?php else: ?>
                    <?php foreach ($security_data['security_log'] as $entry): ?>
                        <?php
                        $class = '';
                        if (strpos($entry, '[WARNING]') !== false) $class = 'warning';
                        if (strpos($entry, '[CRITICAL]') !== false) $class = 'critical';
                        ?>
                        <div class="log-entry <?php echo $class; ?>">
                            <?php echo htmlspecialchars($entry); ?>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>
        
        <div class="refresh-info">
            <p>Dashboard auto-refreshes every 30 seconds</p>
            <p><a href="?refresh=1" style="color: #4a9eff;">Manual Refresh</a> | 
               <a href="?logout=1" style="color: #ff4757;">Logout</a></p>
        </div>
    </div>
    
    <script>
        // Auto-refresh every 30 seconds
        setTimeout(function() {
            window.location.reload();
        }, 30000);
    </script>
</body>
</html>

<?php
// Handle logout
if (isset($_GET['logout'])) {
    session_destroy();
    header('Location: security_dashboard.php');
    exit();
}
?>
