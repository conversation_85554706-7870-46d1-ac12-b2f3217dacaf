#!/usr/bin/env python3
"""
Projects.json Injection Testing Tool
Specifically designed to test injection vulnerabilities in the projects.json system
WARNING: For educational and authorized testing purposes only!
"""

import json
import os
import sys
import subprocess
import tempfile
from datetime import datetime

class ProjectsInjectionTester:
    """Test injection vulnerabilities specific to projects.json system"""
    
    def __init__(self):
        self.malicious_projects = []
        self.test_file = "test_projects.json"
    
    def create_malicious_projects(self):
        """Create projects with various injection payloads"""
        
        # Command injection payloads
        command_injections = [
            "; ls -la",
            "&& whoami",
            "| cat /etc/passwd",
            "; python -c 'import os; os.system(\"whoami\")'",
            "&& rm -rf /tmp/*"
        ]
        
        # SQL injection payloads
        sql_injections = [
            "'; DROP TABLE projects; --",
            "' OR 1=1 --",
            "' UNION SELECT * FROM users --",
            "'; INSERT INTO admin VALUES ('hacked'); --",
            "' OR 'a'='a"
        ]
        
        # Code injection payloads
        code_injections = [
            "__import__('os').system('whoami')",
            "exec('import subprocess; subprocess.call([\"ls\"])')",
            "eval('__import__(\"os\").system(\"pwd\")')",
            "{user.__class__.__bases__[0].__subclasses__()}",
            "'; import os; os.system('echo hacked')"
        ]
        
        # File path injection payloads
        file_injections = [
            "../../../etc/passwd",
            "..\\..\\..\\windows\\system32\\config\\sam",
            "/dev/null; cat /etc/shadow",
            "test.txt; rm important.file",
            "file.txt && python backdoor.py"
        ]
        
        # JSON structure injection payloads
        json_injections = [
            '", "admin": true, "hacked": "yes',
            '}, {"evil": "payload"}, {"normal": "data',
            '": true}, {"__proto__": {"admin": true}}, {"x": "',
            '\\", \\"admin\\": true, \\"x\\": \\"',
            '"}, {"$eval": "malicious_code()"}, {"x": "'
        ]
        
        project_id = 1
        
        # Create projects with command injection in various fields
        for payload in command_injections:
            self.malicious_projects.append({
                "id": project_id,
                "name": f"Command Injection Test {project_id}",
                "description": f"Testing command injection: {payload}",
                "technologies": ["Python", f"Malicious{payload}"],
                "status": "completed",
                "github_url": f"https://github.com/test{payload}",
                "demo_url": f"https://demo.com{payload}",
                "date_created": "2024-01-01",
                "featured": False,
                "buttons": [
                    {
                        "label": f"Inject{payload}",
                        "url": f"https://evil.com{payload}",
                        "type": "primary",
                        "icon": "💀"
                    }
                ]
            })
            project_id += 1
        
        # Create projects with SQL injection
        for payload in sql_injections:
            self.malicious_projects.append({
                "id": project_id,
                "name": f"SQL Injection{payload}",
                "description": f"SQL payload: {payload}",
                "technologies": ["MySQL", "PHP"],
                "status": f"active{payload}",
                "github_url": "https://github.com/test/sql",
                "demo_url": "https://demo.com/sql",
                "date_created": "2024-01-02",
                "featured": False
            })
            project_id += 1
        
        # Create projects with code injection
        for payload in code_injections:
            self.malicious_projects.append({
                "id": project_id,
                "name": "Code Injection Test",
                "description": f"Code execution: {payload}",
                "technologies": ["Python", "Flask"],
                "status": "completed",
                "github_url": "https://github.com/test/code",
                "demo_url": "https://demo.com/code",
                "date_created": "2024-01-03",
                "featured": True,
                "buttons": [
                    {
                        "label": "Execute",
                        "url": "https://evil.com/exec",
                        "type": "primary",
                        "icon": f"{payload}"
                    }
                ]
            })
            project_id += 1
        
        # Create projects with file injection
        for payload in file_injections:
            self.malicious_projects.append({
                "id": project_id,
                "name": "File Injection Test",
                "description": "Testing file access vulnerabilities",
                "technologies": ["Python", "FileSystem"],
                "status": "completed",
                "github_url": f"https://github.com/{payload}",
                "demo_url": "https://demo.com/file",
                "image": f"{payload}",
                "date_created": "2024-01-04",
                "featured": False
            })
            project_id += 1
        
        # Create projects with JSON structure injection
        for payload in json_injections:
            self.malicious_projects.append({
                "id": project_id,
                "name": f"JSON Injection{payload}",
                "description": f"JSON structure attack: {payload}",
                "technologies": ["JSON", "JavaScript"],
                "status": "completed",
                "github_url": "https://github.com/test/json",
                "demo_url": "https://demo.com/json",
                "date_created": "2024-01-05",
                "featured": False
            })
            project_id += 1
    
    def create_test_file(self):
        """Create malicious projects.json test file"""
        self.create_malicious_projects()
        
        test_data = {
            "projects": self.malicious_projects
        }
        
        try:
            with open(self.test_file, 'w') as f:
                json.dump(test_data, f, indent=2)
            print(f"Created malicious test file: {self.test_file}")
            print(f"Total malicious projects: {len(self.malicious_projects)}")
        except Exception as e:
            print(f"Error creating test file: {e}")
    
    def test_json_parsing(self):
        """Test how the system handles malicious JSON"""
        print("\n=== JSON PARSING TESTS ===")
        
        try:
            with open(self.test_file, 'r') as f:
                data = json.load(f)
            
            print("JSON parsed successfully")
            print(f"Projects loaded: {len(data.get('projects', []))}")
            
            # Test individual project processing
            for project in data.get('projects', [])[:5]:  # Test first 5
                print(f"\nTesting project: {project.get('name', 'Unknown')}")
                
                # Simulate field processing
                for field, value in project.items():
                    if isinstance(value, str) and len(value) > 50:
                        print(f"  {field}: {value[:50]}...")
                    else:
                        print(f"  {field}: {value}")
                        
        except Exception as e:
            print(f"JSON parsing error: {e}")
    
    def test_php_simulation(self):
        """Simulate PHP processing of malicious JSON"""
        print("\n=== PHP SIMULATION TESTS ===")
        
        # Create a simple PHP test script
        php_script = '''<?php
// Simulate the projects.php processing
$json_file = "''' + self.test_file + '''";

if (file_exists($json_file)) {
    $json_content = file_get_contents($json_file);
    $data = json_decode($json_content, true);
    
    if ($data && isset($data['projects'])) {
        echo "Processing " . count($data['projects']) . " projects\\n";
        
        foreach ($data['projects'] as $project) {
            echo "Project: " . htmlspecialchars($project['name'] ?? 'Unknown') . "\\n";
            echo "Description: " . htmlspecialchars(substr($project['description'] ?? '', 0, 50)) . "...\\n";
            
            // Test URL validation
            if (!empty($project['github_url'])) {
                $url = filter_var($project['github_url'], FILTER_VALIDATE_URL);
                echo "GitHub URL valid: " . ($url ? "Yes" : "No") . "\\n";
            }
            
            echo "---\\n";
        }
    } else {
        echo "Invalid JSON structure\\n";
    }
} else {
    echo "Test file not found\\n";
}
?>'''
        
        # Write PHP test script
        with open('test_php_processing.php', 'w') as f:
            f.write(php_script)
        
        # Run PHP script if PHP is available
        try:
            result = subprocess.run(['php', 'test_php_processing.php'], 
                                  capture_output=True, text=True, timeout=10)
            print("PHP processing output:")
            print(result.stdout)
            if result.stderr:
                print("PHP errors:")
                print(result.stderr)
        except FileNotFoundError:
            print("PHP not available for testing")
        except Exception as e:
            print(f"PHP test error: {e}")
        finally:
            # Clean up
            try:
                os.remove('test_php_processing.php')
            except:
                pass
    
    def test_security_bypass(self):
        """Test various security bypass techniques"""
        print("\n=== SECURITY BYPASS TESTS ===")
        
        bypass_techniques = [
            # Encoding bypasses
            "%3Bls%20-la",  # URL encoded
            "&#59;ls -la",  # HTML entity
            "\\u003Bls -la",  # Unicode escape
            
            # Case variation bypasses
            "EXEC('malicious')",
            "ExEc('malicious')",
            "eXeC('malicious')",
            
            # Whitespace bypasses
            "exec\t('malicious')",
            "exec\n('malicious')",
            "exec\r('malicious')",
            
            # Comment bypasses
            "exec/*comment*/('malicious')",
            "exec#comment\n('malicious')",
            "exec--comment\n('malicious')",
            
            # Concatenation bypasses
            "ex"+"ec('malicious')",
            "e"+"x"+"e"+"c('malicious')",
            
            # Alternative syntax
            "getattr(__builtins__, 'exec')('malicious')",
            "__import__('os').system('whoami')",
            "vars(__builtins__)['exec']('malicious')"
        ]
        
        for technique in bypass_techniques:
            print(f"Testing bypass: {technique}")
            
            # Create test project with bypass technique
            test_project = {
                "id": 999,
                "name": f"Bypass Test",
                "description": f"Testing: {technique}",
                "technologies": ["Python"],
                "status": "completed",
                "date_created": "2024-01-01",
                "featured": False
            }
            
            # Test JSON encoding
            try:
                json_str = json.dumps(test_project)
                print(f"  JSON encoding: Success")
            except Exception as e:
                print(f"  JSON encoding: Failed - {e}")
            
            print("  " + "-" * 40)
    
    def generate_report(self):
        """Generate a security test report"""
        print("\n=== SECURITY TEST REPORT ===")
        print(f"Test file: {self.test_file}")
        print(f"Total malicious projects: {len(self.malicious_projects)}")
        print(f"Test date: {datetime.now()}")
        
        print("\nInjection types tested:")
        print("- Command injection")
        print("- SQL injection") 
        print("- Code injection")
        print("- File path injection")
        print("- JSON structure injection")
        print("- Security bypass techniques")
        
        print("\nRecommendations:")
        print("1. Implement strict input validation")
        print("2. Use parameterized queries for database operations")
        print("3. Sanitize all user input before processing")
        print("4. Avoid eval() and exec() functions")
        print("5. Validate file paths and restrict file access")
        print("6. Use JSON schema validation")
        print("7. Implement proper error handling")
    
    def cleanup(self):
        """Clean up test files"""
        try:
            os.remove(self.test_file)
            print(f"Cleaned up test file: {self.test_file}")
        except:
            pass
    
    def run_all_tests(self):
        """Run all injection tests"""
        print("Projects.json Injection Testing Tool")
        print("WARNING: For educational and authorized testing only!")
        print("=" * 60)
        
        try:
            self.create_test_file()
            self.test_json_parsing()
            self.test_php_simulation()
            self.test_security_bypass()
            self.generate_report()
        except KeyboardInterrupt:
            print("\nTesting interrupted by user")
        except Exception as e:
            print(f"Error during testing: {e}")
        finally:
            self.cleanup()

def main():
    """Main function"""
    tester = ProjectsInjectionTester()
    tester.run_all_tests()

if __name__ == "__main__":
    main()
