<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>tuber92sv - Home</title>
    <link href="https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@300;400;500;600&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary: #4a9eff;
            --primary-glow: rgba(74, 158, 255, 0.3);
            --bg: #0a0a0a;
            --bg-secondary: #111111;
            --text: #ffffff;
            --text-muted: #888888;
            --border: #222222;
            --grid: rgba(255, 255, 255, 0.03);
        }

        body {
            font-family: 'JetBrains Mono', monospace;
            background-color: var(--bg);
            color: var(--text);
            line-height: 1.6;
            overflow-x: hidden;
            position: relative;
        }

        /* Grid background */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image:
                linear-gradient(var(--grid) 1px, transparent 1px),
                linear-gradient(90deg, var(--grid) 1px, transparent 1px);
            background-size: 50px 50px;
            z-index: -1;
            animation: gridMove 20s linear infinite;
        }

        @keyframes gridMove {
            0% { transform: translate(0, 0); }
            100% { transform: translate(50px, 50px); }
        }

        /* Navigation */
        nav {
            position: fixed;
            top: 0;
            width: 100%;
            padding: 2rem 0;
            background: rgba(10, 10, 10, 0.8);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid var(--border);
            z-index: 100;
        }

        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: center;
            gap: 3rem;
        }

        .nav-link {
            color: var(--text-muted);
            text-decoration: none;
            font-weight: 400;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 2px;
            padding: 0.5rem 0;
            position: relative;
            transition: all 0.3s ease;
        }

        .nav-link.active {
            color: var(--primary);
        }

        .nav-link::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 0;
            height: 2px;
            background: var(--primary);
            transition: width 0.3s ease;
        }

        .nav-link:hover::after,
        .nav-link.active::after {
            width: 100%;
        }

        .nav-link:hover {
            color: var(--primary);
        }

        /* Main content */
        main {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 6rem 2rem 2rem;
        }

        .container {
            max-width: 800px;
            text-align: center;
        }

        .title {
            font-size: clamp(3rem, 8vw, 6rem);
            font-weight: 600;
            color: var(--primary);
            margin-bottom: 2rem;
            letter-spacing: -2px;
            opacity: 0;
            animation: fadeInUp 1s ease-out 0.5s forwards;
            text-shadow: 0 0 50px var(--primary-glow);
        }

        .subtitle {
            font-size: 1.2rem;
            color: var(--text-muted);
            margin-bottom: 3rem;
            font-weight: 300;
            opacity: 0;
            animation: fadeInUp 1s ease-out 0.7s forwards;
        }

        .description {
            font-size: 1rem;
            color: var(--text-muted);
            max-width: 600px;
            margin: 0 auto 4rem;
            opacity: 0;
            animation: fadeInUp 1s ease-out 0.9s forwards;
        }

        .status {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.8rem 1.5rem;
            background: var(--bg-secondary);
            border: 1px solid var(--border);
            border-radius: 30px;
            font-size: 0.9rem;
            color: var(--text-muted);
            font-weight: 400;
            opacity: 0;
            animation: fadeInUp 1s ease-out 1.1s forwards;
            transition: all 0.3s ease;
        }

        .status:hover {
            border-color: var(--primary);
            box-shadow: 0 0 20px var(--primary-glow);
        }

        .status-dot {
            width: 8px;
            height: 8px;
            background: var(--primary);
            border-radius: 50%;
            animation: pulse 2s ease-in-out infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 0.5; transform: scale(1); }
            50% { opacity: 1; transform: scale(1.2); }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Content sections */
        .content-section {
            margin-top: 8rem;
            opacity: 0;
            animation: fadeInUp 1s ease-out 1.3s forwards;
        }

        .section-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin-top: 3rem;
        }

        .card {
            background: var(--bg-secondary);
            border: 1px solid var(--border);
            border-radius: 8px;
            padding: 2rem;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: var(--primary);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .card:hover::before {
            transform: scaleX(1);
        }

        .card:hover {
            border-color: var(--primary);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            transform: translateY(-2px);
        }

        .card h3 {
            color: var(--text);
            font-weight: 500;
            margin-bottom: 1rem;
            font-size: 1.1rem;
        }

        .card p {
            color: var(--text-muted);
            font-size: 0.9rem;
            line-height: 1.7;
        }

        .tags {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            margin-top: 1.5rem;
        }

        .tag {
            background: rgba(74, 158, 255, 0.1);
            border: 1px solid rgba(74, 158, 255, 0.3);
            color: var(--primary);
            padding: 0.3rem 0.8rem;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: 400;
            transition: all 0.3s ease;
        }

        .tag:hover {
            background: rgba(74, 158, 255, 0.2);
            border-color: var(--primary);
        }

        /* Footer note */
        .footer-note {
            margin-top: 6rem;
            padding: 2rem;
            background: rgba(74, 158, 255, 0.05);
            border: 1px solid rgba(74, 158, 255, 0.2);
            border-radius: 8px;
            text-align: center;
            font-size: 0.9rem;
            color: var(--text-muted);
        }

        /* Smooth scrolling */
        html {
            scroll-behavior: smooth;
        }

        /* Section styling */
        .section {
            min-height: 100vh;
            padding: 6rem 2rem 4rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .section-title {
            font-size: clamp(2.5rem, 6vw, 4rem);
            font-weight: 600;
            color: var(--primary);
            margin-bottom: 3rem;
            text-align: center;
            letter-spacing: -1px;
            text-shadow: 0 0 30px var(--primary-glow);
        }

        /* Contact section specific styles */
        .contact-content {
            text-align: center;
            max-width: 600px;
        }

        .contact-info {
            background: var(--bg-secondary);
            border: 1px solid var(--border);
            border-radius: 12px;
            padding: 3rem 2rem;
            margin-top: 2rem;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .contact-info::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, transparent, var(--primary), transparent);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .contact-info:hover::before {
            transform: scaleX(1);
        }

        .contact-info:hover {
            border-color: var(--primary);
            box-shadow: 0 8px 32px rgba(74, 158, 255, 0.1);
            transform: translateY(-2px);
        }

        .contact-text {
            font-size: 1.2rem;
            color: var(--text);
            margin-bottom: 1rem;
        }

        .discord-handle {
            font-size: 1.4rem;
            color: var(--primary);
            font-weight: 500;
            font-family: 'JetBrains Mono', monospace;
            background: rgba(74, 158, 255, 0.1);
            padding: 0.8rem 1.5rem;
            border-radius: 8px;
            border: 1px solid rgba(74, 158, 255, 0.3);
            display: inline-block;
            margin-top: 1rem;
            transition: all 0.3s ease;
        }

        .discord-handle:hover {
            background: rgba(74, 158, 255, 0.2);
            border-color: var(--primary);
            box-shadow: 0 0 20px var(--primary-glow);
        }

        /* Projects section specific styles */
        .projects-content {
            max-width: 1000px;
            width: 100%;
            text-align: center;
        }

        .projects-empty {
            background: var(--bg-secondary);
            border: 2px dashed var(--border);
            border-radius: 12px;
            padding: 4rem 2rem;
            margin-top: 3rem;
            transition: all 0.3s ease;
        }

        .projects-empty:hover {
            border-color: rgba(74, 158, 255, 0.5);
            background: rgba(74, 158, 255, 0.02);
        }

        .projects-empty-icon {
            font-size: 3rem;
            color: var(--text-muted);
            margin-bottom: 1.5rem;
            opacity: 0.5;
        }

        .projects-empty-text {
            font-size: 1.1rem;
            color: var(--text-muted);
            margin-bottom: 0.5rem;
        }

        .projects-empty-subtext {
            font-size: 0.9rem;
            color: var(--text-muted);
            opacity: 0.7;
        }

        .projects-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-top: 3rem;
        }

        .project-card {
            background: var(--bg-secondary);
            border: 1px solid var(--border);
            border-radius: 12px;
            padding: 2.5rem;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            text-align: left;
        }

        .project-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, var(--primary), rgba(74, 158, 255, 0.5));
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .project-card:hover::before {
            transform: scaleX(1);
        }

        .project-card:hover {
            border-color: var(--primary);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.3);
            transform: translateY(-4px);
        }

        .project-title {
            color: var(--text);
            font-weight: 500;
            margin-bottom: 1rem;
            font-size: 1.3rem;
        }

        .project-description {
            color: var(--text-muted);
            font-size: 0.95rem;
            line-height: 1.7;
            margin-bottom: 1.5rem;
        }

        .project-tech {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
        }

        .tech-tag {
            background: rgba(74, 158, 255, 0.1);
            border: 1px solid rgba(74, 158, 255, 0.3);
            color: var(--primary);
            padding: 0.4rem 0.8rem;
            border-radius: 6px;
            font-size: 0.8rem;
            font-weight: 400;
            transition: all 0.3s ease;
        }

        .tech-tag:hover {
            background: rgba(74, 158, 255, 0.2);
            border-color: var(--primary);
        }

        /* Section separator */
        .section-separator {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 4rem 0;
            position: relative;
        }

        .separator-char {
            font-family: 'JetBrains Mono', monospace;
            font-size: 3rem;
            font-weight: 300;
            color: var(--primary);
            opacity: 0.6;
            text-shadow: 0 0 20px var(--primary-glow);
            transition: all 0.3s ease;
            position: relative;
            z-index: 1;
        }

        .separator-char:hover {
            opacity: 1;
            transform: scale(1.1);
            text-shadow: 0 0 30px var(--primary-glow);
        }

        .separator-char::before,
        .separator-char::after {
            content: '';
            position: absolute;
            top: 50%;
            width: 100px;
            height: 1px;
            background: linear-gradient(to right, transparent, var(--border), transparent);
            opacity: 0.3;
        }

        .separator-char::before {
            right: 100%;
            margin-right: 2rem;
        }

        .separator-char::after {
            left: 100%;
            margin-left: 2rem;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .nav-container {
                gap: 2rem;
            }

            .nav-link {
                font-size: 0.8rem;
            }

            .section {
                padding: 8rem 1rem 4rem;
            }

            .section-grid {
                grid-template-columns: 1fr;
            }

            .projects-grid {
                grid-template-columns: 1fr;
            }

            .contact-info {
                padding: 2rem 1.5rem;
            }

            .projects-empty {
                padding: 3rem 1.5rem;
            }

            .section-separator {
                padding: 3rem 0;
            }

            .separator-char {
                font-size: 2.5rem;
            }

            .separator-char::before,
            .separator-char::after {
                width: 60px;
                margin-left: 1.5rem;
                margin-right: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <nav>
        <div class="nav-container">
            <a href="bio.html" class="nav-link">BIO</a>
            <a href="contact.html" class="nav-link">CONTACT</a>
            <a href="projects.html" class="nav-link">PROJECTS</a>
        </div>
    </nav>

    <!-- Bio Section -->
    <section id="bio" class="section">
        <div class="container">
            <h1 class="title">TUBER92SV</h1>
            <p class="subtitle">gaming / ai enthusiast / paleontology</p>

            <p class="description">
                I play games and hang out with friends online. This isn't my real website but my friend's.
                I'm into gaming, artificial intelligence, and fascinated by dinosaurs.
            </p>

            <div class="status">
                <div class="status-dot"></div>
                website still being made
            </div>

            <div class="content-section">
                <div class="section-grid">
                    <div class="card">
                        <h3>Gaming</h3>
                        <p>Passionate about multiplayer gaming and building connections through shared experiences in virtual worlds.</p>
                        <div class="tags">
                            <span class="tag">roblox</span>
                            <span class="tag">minecraft</span>
                            <span class="tag">multiplayer</span>
                        </div>
                    </div>

                    <div class="card">
                        <h3>Artificial Intelligence</h3>
                        <p>Fascinated by the rapid evolution of AI technology and its potential to transform our daily lives.</p>
                        <div class="tags">
                            <span class="tag">machine learning</span>
                            <span class="tag">future tech</span>
                            <span class="tag">innovation</span>
                        </div>
                    </div>

                    <div class="card">
                        <h3>Paleontology</h3>
                        <p>Deeply interested in prehistoric life and the incredible diversity of creatures that once inhabited Earth.</p>
                        <div class="tags">
                            <span class="tag">dinosaurs</span>
                            <span class="tag">evolution</span>
                            <span class="tag">natural history</span>
                        </div>
                    </div>
                </div>

                <div class="footer-note">
                    hosted on my friend's website - thanks for the space
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="section">
        <div class="contact-content">
            <h2 class="section-title">CONTACT</h2>
            <div class="contact-info">
                <p class="contact-text">Want to get in touch?</p>
                <div class="discord-handle">tuber92sv</div>
                <p style="color: var(--text-muted); font-size: 0.9rem; margin-top: 1rem;">
                    Feel free to reach out on Discord for gaming, AI discussions, or just to chat!
                </p>
            </div>
        </div>
    </section>

    <!-- Section Separator -->
    <div class="section-separator">
        <div class="separator-char">/</div>
    </div>

    <!-- Projects Section -->
    <section id="projects" class="section">
        <div class="projects-content">
            <h2 class="section-title">PROJECTS</h2>
            <div class="projects-empty">
                <div class="projects-empty-icon">⚡</div>
                <p class="projects-empty-text">No projects yet</p>
                <p class="projects-empty-subtext">This space is ready for future projects and creations</p>
            </div>
        </div>
    </section>


</body>
</html>