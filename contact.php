<?php
/**
 * Contact Page - Contact information for tuber92sv
 * MAXIMUM SECURITY IMPLEMENTATION
 *
 * Security measures implemented:
 * - Enterprise-level security configuration
 * - Enhanced rate limiting for contact page
 * - Advanced input validation and sanitization
 * - Ultra-strict Content Security Policy
 * - CSRF protection for contact forms
 * - Session security hardening
 * - IP monitoring and blacklisting
 * - Comprehensive security logging
 */

// Include ultra-security configuration
require_once 'security_config.php';

// Start output buffering to prevent header issues
ob_start();

// Safe input validation with fallback
global $ultra_security;

// Simple input validation function as fallback
function safeValidateInput($input) {
    if (empty($input)) return '';
    $input = trim($input);
    $input = stripslashes($input);
    $input = htmlspecialchars($input, ENT_QUOTES, 'UTF-8');
    return $input;
}

// Enhanced security for contact page (with fallback)
if (!empty($_GET)) {
    foreach ($_GET as $key => $value) {
        if ($ultra_security) {
            $_GET[$key] = $ultra_security->ultraValidateInput($value, 'alphanumeric', 50);
        } else {
            $_GET[$key] = safeValidateInput($value);
        }
    }
}

if (!empty($_POST)) {
    foreach ($_POST as $key => $value) {
        if ($ultra_security) {
            $_POST[$key] = $ultra_security->ultraValidateInput($value, 'string', 200);
        } else {
            $_POST[$key] = safeValidateInput($value);
        }
    }
}

// Page-specific security: Ensure this is accessed as intended
$current_page = basename($_SERVER['PHP_SELF']);
if ($current_page !== 'contact.php') {
    header('Location: contact.php', true, 302);
    exit();
}

// Additional security for contact page: Enhanced monitoring
$user_ip = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
if ($ultra_security) {
    $ultra_security->logSecurityEvent("Contact page accessed", 'INFO');
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>tuber92sv - Contact</title>
    <link href="https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@300;400;500;600&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary: #4a9eff;
            --primary-glow: rgba(74, 158, 255, 0.3);
            --bg: #0a0a0a;
            --bg-secondary: #111111;
            --text: #ffffff;
            --text-muted: #888888;
            --border: #222222;
            --grid: rgba(255, 255, 255, 0.03);
        }

        body {
            font-family: 'JetBrains Mono', monospace;
            background-color: var(--bg);
            color: var(--text);
            line-height: 1.6;
            overflow-x: hidden;
            position: relative;
        }

        /* Grid background */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image:
                linear-gradient(var(--grid) 1px, transparent 1px),
                linear-gradient(90deg, var(--grid) 1px, transparent 1px);
            background-size: 50px 50px;
            z-index: -1;
            animation: gridMove 20s linear infinite;
        }

        @keyframes gridMove {
            0% { transform: translate(0, 0); }
            100% { transform: translate(50px, 50px); }
        }

        /* Navigation */
        nav {
            position: fixed;
            top: 0;
            width: 100%;
            padding: 2rem 0;
            background: rgba(10, 10, 10, 0.8);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid var(--border);
            z-index: 100;
        }

        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: center;
            gap: 3rem;
        }

        .nav-link {
            color: var(--text-muted);
            text-decoration: none;
            font-weight: 400;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 2px;
            padding: 0.5rem 0;
            position: relative;
            transition: all 0.3s ease;
        }

        .nav-link.active {
            color: var(--primary);
        }

        .nav-link::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 0;
            height: 2px;
            background: var(--primary);
            transition: width 0.3s ease;
        }

        .nav-link:hover::after,
        .nav-link.active::after {
            width: 100%;
        }

        .nav-link:hover {
            color: var(--primary);
        }

        /* Main content */
        main {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 6rem 2rem 4rem;
        }

        .contact-content {
            text-align: center;
            max-width: 600px;
        }

        .section-title {
            font-size: clamp(2.5rem, 6vw, 4rem);
            font-weight: 600;
            color: var(--primary);
            margin-bottom: 3rem;
            text-align: center;
            letter-spacing: -1px;
            text-shadow: 0 0 30px var(--primary-glow);
            opacity: 0;
            animation: fadeInUp 1s ease-out 0.5s forwards;
        }

        .contact-info {
            background: var(--bg-secondary);
            border: 1px solid var(--border);
            border-radius: 12px;
            padding: 3rem 2rem;
            margin-top: 2rem;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            opacity: 0;
            animation: fadeInUp 1s ease-out 0.7s forwards;
        }

        .contact-info::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, transparent, var(--primary), transparent);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .contact-info:hover::before {
            transform: scaleX(1);
        }

        .contact-info:hover {
            border-color: var(--primary);
            box-shadow: 0 8px 32px rgba(74, 158, 255, 0.1);
            transform: translateY(-2px);
        }

        .contact-text {
            font-size: 1.2rem;
            color: var(--text);
            margin-bottom: 1rem;
        }

        .discord-handle {
            font-size: 1.4rem;
            color: var(--primary);
            font-weight: 500;
            font-family: 'JetBrains Mono', monospace;
            background: rgba(74, 158, 255, 0.1);
            padding: 0.8rem 1.5rem;
            border-radius: 8px;
            border: 1px solid rgba(74, 158, 255, 0.3);
            display: inline-block;
            margin-top: 1rem;
            transition: all 0.3s ease;
        }

        .discord-handle:hover {
            background: rgba(74, 158, 255, 0.2);
            border-color: var(--primary);
            box-shadow: 0 0 20px var(--primary-glow);
        }

        .contact-subtext {
            color: var(--text-muted);
            font-size: 0.9rem;
            margin-top: 1rem;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Responsive */
        @media (max-width: 768px) {
            .nav-container {
                gap: 2rem;
            }

            .nav-link {
                font-size: 0.8rem;
            }

            main {
                padding: 8rem 1rem 4rem;
            }

            .contact-info {
                padding: 2rem 1.5rem;
            }
        }
    </style>
</head>
<body>
    <nav>
        <div class="nav-container">
            <a href="bio.php" class="nav-link">BIO</a>
            <a href="contact.php" class="nav-link active">CONTACT</a>
            <a href="projects.php" class="nav-link">PROJECTS</a>
        </div>
    </nav>

    <main>
        <div class="contact-content">
            <h2 class="section-title">CONTACT</h2>
            <div class="contact-info">
                <p class="contact-text">Want to get in touch?</p>
                <div class="discord-handle">tuber92sv</div>
                <p class="contact-subtext">
                    Feel free to reach out on Discord for gaming, AI discussions, or just to chat!
                </p>
            </div>
        </div>
    </main>
</body>
</html>
<?php
// End output buffering and flush content
ob_end_flush();

// Additional security: Clear any sensitive variables
unset($current_page, $user_ip);

// Log successful page load with enhanced security logging for contact page
error_log("Contact page accessed from IP: " . ($_SERVER['REMOTE_ADDR'] ?? 'unknown') . " at " . date('Y-m-d H:i:s'));
?>
