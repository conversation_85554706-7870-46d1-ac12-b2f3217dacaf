<?php
/**
 * Default redirect handler
 * Redirects users to the main bio page
 * 
 * Security measures implemented:
 * - Server-side redirect (no client-side code exposure)
 * - Proper HTTP status codes
 * - Input validation and sanitization
 * - Exit after redirect to prevent code execution
 * - Security headers to prevent various attacks
 */

// Security headers to prevent various attacks
header('X-Content-Type-Options: nosniff');
header('X-Frame-Options: DENY');
header('X-XSS-Protection: 1; mode=block');
header('Referrer-Policy: strict-origin-when-cross-origin');

// Prevent caching of this redirect page
header('Cache-Control: no-cache, no-store, must-revalidate');
header('Pragma: no-cache');
header('Expires: 0');

// Define the target redirect URL
$redirect_url = 'bio.php';

// Validate the redirect URL (basic security check)
if (!empty($redirect_url) && preg_match('/^[a-zA-Z0-9._-]+\.php$/', $redirect_url)) {
    // Check if the target file exists (optional security measure)
    if (file_exists($redirect_url)) {
        // Perform the redirect with proper HTTP status code
        header('Location: ' . $redirect_url, true, 302);
        exit(); // Critical: prevent any further code execution
    }
}

// Fallback: if redirect fails, show minimal error page
// This should rarely execute if bio.php exists
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Redirect - tuber92sv</title>
    <meta name="robots" content="noindex, nofollow">
    <style>
        body {
            font-family: 'JetBrains Mono', monospace;
            background: #0a0a0a;
            color: #ffffff;
            text-align: center;
            padding: 2rem;
            margin: 0;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            padding: 2rem;
        }
        .error {
            color: #ff6b6b;
            margin-bottom: 1rem;
        }
        a {
            color: #4a9eff;
            text-decoration: none;
        }
        a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="error">Redirect Error</h1>
        <p>Unable to automatically redirect. Please try one of these links:</p>
        <p><a href="bio.php">Bio Page</a></p>
        <p><a href="contact.php">Contact Page</a></p>
        <p><a href="projects.php">Projects Page</a></p>
    </div>
</body>
</html>
